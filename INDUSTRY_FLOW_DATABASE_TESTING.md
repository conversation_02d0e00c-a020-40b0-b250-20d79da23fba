# Industry Flow Database Integration Testing Guide

## Overview

This document explains how to test the comprehensive save/restore functionality for industry flows that will be integrated with the database. The implementation includes a complete schema that captures all necessary data to fully restore an industry flow.

## What Gets Saved (Simplified Schema)

The new `IndustryFlowSchema` uses a **simplified, non-duplicated approach**:

### 1. **Essential Node Data Only**
- **Node Information**: ID, label, type, and coordinates
- **Complete Form Data**: All 4-tab data embedded with each node
  - Inputs tab (materials, energy, emissions) → generates standalone input edges
  - Outputs tab (materials, energy, final outputs) → generates output edges & final components
  - By-products tab (energy and material by-products) → generates byproduct edges
  - Financial tab (capacity, costs, etc.)
- **Technology Data**: Selected technologies and configurations

### 2. **Generated Elements (Not Stored)**
- **Edges**: Generated from form data (inputs, outputs, byproducts)
- **Final Output Components**: Created from final output flags in form data
- **Standalone Input Edges**: Created from inputs with sourceActivity = 'Nil'
- **Visual Styling**: Applied during generation

### 3. **Benefits of This Approach**
- ✅ **No Data Duplication**: Edges derived from form data, not stored separately
- ✅ **Single Source of Truth**: Form data drives all visual elements
- ✅ **Consistency Guaranteed**: Layout always matches form data
- ✅ **Smaller Database Size**: Only essential data stored
- ✅ **Easier Maintenance**: Changes to form data automatically update visuals

## How to Test

### Step 1: Access the Industry Flow
1. Navigate to the Industry Flow page
2. You'll see four new test buttons in the header:
   - **"Test Save"** (green button) - Save current state
   - **"Test Restore"** (blue button) - Restore from saved state
   - **"Clear & Restore"** (purple button) - Clear everything then restore
   - **"Make Changes"** (orange button) - Make obvious changes for testing

### Step 2: Create or Load a Flow
1. Either:
   - Use "Use Standard Template" to load the default gas processing flow
   - Or build a flow from scratch
   - Or load an existing saved flow

### Step 3: Add Form Data (Important!)
1. Double-click on nodes to open their connection forms
2. Fill out the 4 tabs with data:
   - **Inputs**: Add materials, energy sources, emissions
   - **Outputs**: Configure outputs and mark final outputs
   - **By-products**: Add any by-products
   - **Financial**: Set capacity and cost data
3. Save each node's form data

### Step 4: Test Save Functionality
1. Give your flow a name (double-click the title to edit)
2. Click the **"Test Save"** button
3. Check the browser console for detailed save information
4. You should see a success toast message

### Step 5: Test Restore Functionality (Multiple Methods)

#### Method A: Simple Restore Test
1. Click **"Make Changes"** button to randomize node positions and modify data
2. Click **"Test Restore"** button
3. Check console logs to see before/after comparison
4. Verify restoration worked correctly

#### Method B: Clear & Restore Test (Most Definitive)
1. Click **"Clear & Restore"** button
2. Watch the flow completely disappear for 1 second
3. See it restore from saved data automatically
4. This proves the data comes from storage, not current state

#### Method C: Manual Changes Test
1. Manually move nodes around the canvas
2. Change the flow name (double-click title)
3. Click **"Test Restore"** button
4. Verify everything returns to saved state

## What to Verify

### Visual Consistency
- [ ] Nodes appear in exact same positions
- [ ] All edges are properly connected
- [ ] Edge labels match original flow
- [ ] Final output nodes are correctly positioned
- [ ] Standalone input edges are preserved

### Data Integrity
- [ ] All 4-tab form data is preserved for each node
- [ ] Technology selections are maintained
- [ ] Material/energy quantities are correct
- [ ] Final output flags are preserved
- [ ] Financial data is intact

### Flow Functionality
- [ ] Can double-click nodes to edit forms
- [ ] Can create new connections
- [ ] Can save the restored flow again
- [ ] All interactive features work normally

## Technical Details

### Storage Location (Testing)
- Test data is saved to localStorage with keys like `industry-flow-test-{timestamp}`
- Check browser DevTools > Application > Local Storage to see saved data

### Simplified Schema Structure
```typescript
interface IndustryFlowSchema {
  name: string;
  flowType: 'inventory' | 'scenario';
  createdAt: string;
  updatedAt: string;
  nodeData: Record<string, NodeDataWithFormData>; // All data per node
  sectorUuid?: string;
  industryId?: string;
}

interface NodeDataWithFormData {
  id: string;
  label: string;
  type: string;
  position: { x: number; y: number };    // Coordinates embedded here
  formData: {                            // Complete 4-tab data (clean structure)
    activity: string;
    currentTechnology: string;           // Currently selected technology

    // Inputs tab (full names, no abbreviations)
    materialInputs: MaterialInput[];     // → generates standalone input edges
    energyInputs: EnergyInput[];
    emissions: EmissionInput[];

    // Outputs tab (moved from separate field)
    outputs: OutputConfiguration[];      // → generates output edges & final components

    // By-products tab (full names)
    materialByProducts: MaterialByProduct[]; // → generates byproduct edges
    energyByProducts: EnergyByProduct[];

    // Financial tab (full names)
    financial: {
      capacity: string;
      capitalCostUnit: string;
      operatingAndMaintenanceCost: string; // Full name instead of 'omCost'
    };
  };
  // Technology data with embedded form data per technology
  technologies: TechnologyData[];        // [{ name: "T1", formData: {...} }]
  completedAt: string;
}

// Clean field names (no abbreviations)
interface MaterialInput {
  material: string;
  specificMaterialCost: string;          // Instead of 'smc'
  sourceActivity: string;                // 'Nil' = standalone input edge
}

interface MaterialOutput {
  material: string;
  specificMaterialCost: string;
  isFinalOutput: boolean;                // Instead of 'final'
  connectToNode: string;                 // Instead of 'connect'
  quantity: string;                      // Instead of 'qty'
  quantityUnit: string;                  // Instead of 'qtyUnit'
}

interface TechnologyData {
  name: string;
  formData: {
    activity: string;
    technology: string;
    parameters?: Record<string, any>;
    // Technology-specific overrides if needed
  };
}
```

### Enhanced Console Logging
The test functions provide detailed console output to prove restoration works:

#### Save Operation Logs:
- Complete schema with all data
- Node count, edge count, form data entries
- Timestamps and meta information

#### Restore Operation Logs:
- **BEFORE RESTORE**: Current state (nodes, edges, positions, form data)
- **RESTORING FROM SAVED DATA**: What's being restored
- **AFTER RESTORE**: Confirmation of restoration completion
- Detailed comparison showing the difference

#### Clear & Restore Logs:
- **CLEARING CURRENT STATE**: Confirmation of state clearing
- **RESTORING FROM SAVED DATA**: What's being restored
- Step-by-step restoration process

This logging definitively proves that data is coming from saved storage, not persisting from current state.

## Next Steps

Once testing confirms the save/restore functionality works correctly:

1. **API Integration**: Replace localStorage with actual database API calls
2. **Error Handling**: Add robust error handling for network issues
3. **Validation**: Add schema validation before save/restore
4. **Performance**: Optimize for large flows with many nodes
5. **Versioning**: Add schema versioning for future compatibility

## Troubleshooting

### Common Issues
1. **"No Test Data" Error**: Save a flow first before trying to restore
2. **Form Data Missing**: Ensure you've filled out node forms before saving
3. **Visual Inconsistencies**: Check that all nodes have proper position data
4. **Console Errors**: Check browser console for detailed error messages

### Debug Tips
1. Open browser DevTools console before testing
2. Check localStorage for saved test data
3. Verify form data is populated by double-clicking nodes
4. Test with both simple and complex flows

## Expected Behavior

A successful test cycle should:
1. Save the complete flow state to localStorage
2. Allow modifications to the current flow
3. Restore the flow to exactly the saved state
4. Preserve all visual and data integrity
5. Enable continued editing after restoration

This comprehensive testing ensures the database integration will work seamlessly when implemented.
