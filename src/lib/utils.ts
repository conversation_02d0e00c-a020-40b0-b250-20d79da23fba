
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Calculate a good position for a new node relative to an existing node
 * 
 * @param sourceNode The source node to position relative to
 * @param nodes All existing nodes in the diagram
 * @param direction Direction to place the node ('right', 'left', 'top', 'bottom')
 * @returns {x: number, y: number} The calculated position
 */
export function calculateNodePosition(sourceNode: any, nodes: any[], direction = 'right') {
  if (!sourceNode) {
    // Default position if no source node
    return { x: 300, y: 200 };
  }
  
  // Base position from source node
  const baseX = sourceNode.position.x;
  const baseY = sourceNode.position.y;
  
  // Default offsets
  const horizontalOffset = 250;
  const verticalOffset = 150;
  
  // Calculate position based on direction
  let newX = baseX;
  let newY = baseY;
  
  switch (direction) {
    case 'right':
      newX = baseX + horizontalOffset;
      break;
    case 'left':
      newX = baseX - horizontalOffset;
      break;
    case 'top':
      newY = baseY - verticalOffset;
      break;
    case 'bottom':
      newY = baseY + verticalOffset;
      break;
    default:
      newX = baseX + horizontalOffset;
  }
  
  // Check for overlapping nodes and adjust if necessary
  const isOverlapping = nodes.some(node => {
    const distance = Math.sqrt(
      Math.pow(node.position.x - newX, 2) + 
      Math.pow(node.position.y - newY, 2)
    );
    return distance < 100; // Consider nodes closer than 100px as overlapping
  });
  
  if (isOverlapping) {
    // If overlapping, offset a bit more
    if (direction === 'right' || direction === 'left') {
      newY += 100; // Offset vertically if horizontal placement
    } else {
      newX += 100; // Offset horizontally if vertical placement
    }
  }
  
  return { x: newX, y: newY };
}
