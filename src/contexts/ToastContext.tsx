
import React, { createContext, useContext } from 'react';
import { toast, useToast } from '@/hooks/use-toast';

type ToastContextType = ReturnType<typeof useToast>;

// Create context with default values
const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const toastUtils = useToast();
  
  return (
    <ToastContext.Provider value={toastUtils}>
      {children}
    </ToastContext.Provider>
  );
}

export const useToastContext = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToastContext must be used within a ToastProvider');
  }
  return context;
};

export { toast };
