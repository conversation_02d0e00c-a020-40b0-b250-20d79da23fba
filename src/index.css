
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 168 41% 96%;
    --foreground: 220 24% 11%;
    --card: 0 0% 100%;
    --card-foreground: 217 30% 18%;
    --popover: 0 0% 100%;
    --popover-foreground: 217 30% 18%;
    --primary: 167 65% 55%;
    --primary-foreground: 217 33% 11%;
    --secondary: 160 56% 90%;
    --secondary-foreground: 217 33% 11%;
    --muted: 220 14% 96%;
    --muted-foreground: 215 20% 45%;
    --accent: 167 55% 90%;
    --accent-foreground: 217 23% 24%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 167 30% 88%;
    --input: 167 30% 88%;
    --ring: 159 60% 55%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 220 24% 11%;
    --foreground: 0 0% 98%;
    --card: 217 30% 18%;
    --card-foreground: 0 0% 100%;
    --popover: 217 30% 18%;
    --popover-foreground: 0 0% 100%;
    --primary: 167 65% 55%;
    --primary-foreground: 217 33% 11%;
    --secondary: 216 34% 17%;
    --secondary-foreground: 0 0% 98%;
    --muted: 217 23% 24%;
    --muted-foreground: 215 20% 70%;
    --accent: 217 23% 24%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 217 26% 24%;
    --input: 217 26% 24%;
    --ring: 159 60% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }
  
  .glass-card {
    @apply bg-white/90 border border-recrea-mint rounded-xl shadow-sm transition-all hover:shadow-md hover:shadow-recrea-turquoise/10 dark:bg-recrea-card-bg/60 dark:border-recrea-card-border;
  }

  .glass-input {
    @apply bg-white/80 border border-recrea-mint rounded-lg px-4 py-3 text-foreground focus:border-recrea-turquoise focus:ring-1 focus:ring-recrea-turquoise dark:bg-white/10 dark:text-white dark:border-recrea-card-border;
  }
  
  .primary-button {
    @apply bg-recrea-turquoise text-white font-medium rounded-full px-5 py-3 transition-all hover:bg-recrea-teal active:scale-95 disabled:opacity-50;
  }
  
  .secondary-button {
    @apply bg-recrea-light-mint text-recrea-dark font-medium rounded-full px-5 py-3 transition-all border border-recrea-mint hover:border-recrea-turquoise active:scale-95 disabled:opacity-50 dark:bg-recrea-card-bg/60 dark:text-white dark:border-recrea-card-border;
  }
  
  .green-text {
    @apply text-recrea-turquoise;
  }

  .hero-section {
    @apply relative overflow-hidden;
  }

  .hero-section::before {
    @apply content-[''] absolute inset-0 bg-opacity-10 bg-recrea-turquoise dark:bg-opacity-5 -z-10;
  }
  
  .teal-gradient {
    @apply bg-gradient-to-br from-recrea-teal to-recrea-turquoise;
  }
  
  .mint-gradient {
    @apply bg-gradient-to-br from-recrea-turquoise to-recrea-mint;
  }
  
  .objective-tag {
    @apply px-4 py-2 rounded-full border border-recrea-mint text-foreground text-sm font-medium transition-all hover:bg-recrea-mint/10 dark:border-white/30 dark:text-white dark:hover:bg-white/10;
  }
  
  .animate-pulse-green {
    animation: pulseGreen 3s infinite;
  }
  
  @keyframes pulseGreen {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(94, 206, 198, 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(94, 206, 198, 0);
    }
  }

  /* Flow Diagram Styles */
  .react-flow__node {
    padding: 0;
    border-radius: 0;
    border-width: initial;
    border: none;
    background: none;
    box-shadow: none;
  }
  .custom-node {
    border: none !important;
    background: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
  .react-flow__renderer .custom-node {
    border: none !important;
    background: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
  .react-flow .react-flow__node {
    border: none !important;
    box-shadow: none !important;
  }
  .react-flow__node.process-node {
    background-color: #f8e0ff;
    border-color: #e2c1f7;
    color: #555;
    min-width: 120px;
  }
  .react-flow__node.light-node {
    background-color: #e0f7ff;
    border-color: #c1e2f7;
    color: #555;
  }
  .react-flow__node.sub-node {
    background-color: #e0ffe0;
    border-color: #c1f7c1;
    color: #555;
  }
  .dark-flow .react-flow__node.process-node {
    background-color: #4d3658;
    border-color: #6d4e7d;
    color: #fff;
  }
  .dark-flow .react-flow__node.light-node {
    background-color: #36424d;
    border-color: #4e617d;
    color: #fff;
  }
  .dark-flow .react-flow__node.sub-node {
    background-color: #364d36;
    border-color: #4e7d4e;
    color: #fff;
  }
  .react-flow__edge-path {
    stroke-width: 2;
  }
  .react-flow__edge.main-flow .react-flow__edge-path {
    stroke: #3b82f6;
  }
  .react-flow__edge.sub-flow .react-flow__edge-path {
    stroke: #22c55e;
  }
  .react-flow__edge.byproduct-flow .react-flow__edge-path {
    stroke: #eab308;
  }
  .dark-flow .react-flow__edge.main-flow .react-flow__edge-path {
    stroke: #60a5fa;
  }
  .dark-flow .react-flow__edge.sub-flow .react-flow__edge-path {
    stroke: #4ade80;
  }
  .dark-flow .react-flow__edge.byproduct-flow .react-flow__edge-path {
    stroke: #facc15;
  }
  .react-flow__edge-text {
    font-size: 11px;
    fill: #555;
  }
  .dark-flow .react-flow__edge-text {
    fill: #bbb;
  }

  /* Final Output Node Hover Effects */
  .final-output-node:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 12px 40px 0 rgba(245,158,11,0.35), 0 0 0 2px rgba(245,158,11,0.2) !important;
  }
}
