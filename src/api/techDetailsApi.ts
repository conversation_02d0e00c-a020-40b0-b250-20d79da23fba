export async function fetchTechnologyByName(techName: string) {
  const token = localStorage.getItem('accessToken');
  const res = await fetch(`https://api.recre8.earth/resources/technologies/name/${techName}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to fetch technology details');
  return await res.json();
}

export async function fetchSuppliersByTechnology(techName: string) {
  const token = localStorage.getItem('accessToken');
  // const res = await fetch(`https://api.recre8.earth/suppliers/by-technology/${techName}`, {
  const res = await fetch(`https://api.recre8.earth/suppliers`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
  if (!res.ok) throw new Error('Failed to fetch suppliers');
  return await res.json();
} 