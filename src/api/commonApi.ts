// Common API utilities for the app

export const API_BASE_URL = 'https://api.recre8.earth';

export function getAuthHeaders() {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  };
}

export async function fetchRegions() {
  const res = await fetch(`${API_BASE_URL}/regions`, {
    method: 'GET',
    headers: getAuthHeaders(),
  });
  if (!res.ok) throw new Error('Failed to fetch regions');
  return await res.json();
} 