
import React, { useState } from 'react';
import { Search, MessageSquare, Clock, ArrowRight } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const SearchInterface: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const { toast } = useToast();

  const handleSearch = (type: string) => {
    if (!searchQuery.trim()) {
      toast({
        title: "Empty Search",
        description: "Please enter a search query",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    
    // Simulate search
    setTimeout(() => {
      toast({
        title: `${type} Search Completed`,
        description: `Results for: ${searchQuery}`,
      });
      setIsSearching(false);
    }, 1500);
  };

  return (
    <div className="w-full max-w-4xl mx-auto animate-fade-in">
      <div className="glass-card p-8">
        <h2 className="text-2xl font-semibold text-center mb-6">
          Get to know everything, <span className="text-recrea-green">let us chat</span>
        </h2>
        
        <Tabs defaultValue="specifics" className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="specifics" className="data-[state=active]:text-recrea-green">
              Search Specifics
            </TabsTrigger>
            <TabsTrigger value="comprehensive" className="data-[state=active]:text-recrea-green">
              Search Comprehensively
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="specifics" className="space-y-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Ask about specific topics or data"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="glass-input w-full pr-12"
              />
            </div>
            
            <Button 
              onClick={() => handleSearch('Specific')}
              disabled={isSearching}
              className="secondary-button w-full flex items-center justify-center gap-2 group"
            >
              <Search size={18} />
              <span>{isSearching ? "Searching..." : "Search Specifics"}</span>
            </Button>
          </TabsContent>
          
          <TabsContent value="comprehensive" className="space-y-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Ask for comprehensive analysis"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="glass-input w-full pr-12"
              />
            </div>
            
            <Button 
              onClick={() => handleSearch('Comprehensive')}
              disabled={isSearching}
              className="secondary-button w-full flex items-center justify-center gap-2 group"
            >
              <Search size={18} />
              <span>{isSearching ? "Searching..." : "Search Comprehensively"}</span>
            </Button>
          </TabsContent>
        </Tabs>
        
        <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
          <Button className="secondary-button flex items-center justify-center gap-2 group">
            <MessageSquare size={18} className="text-recrea-green group-hover:animate-pulse-green" />
            <span>Start Chat</span>
          </Button>
          
          <Button className="secondary-button flex items-center justify-center gap-2 group">
            <Clock size={18} className="text-recrea-green group-hover:animate-pulse-green" />
            <span>Access Past Interactions</span>
          </Button>
          
          <Button className="secondary-button flex items-center justify-center gap-2 group">
            <ArrowRight size={18} className="text-recrea-green group-hover:animate-pulse-green" />
            <span>Move to Detailed Optimization</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SearchInterface;
