
import React from 'react';
import { Button } from '@/components/ui/button';

interface FilterCategoriesProps {
  categories: string[];
  selectedCategory: string;
  setSelectedCategory: (category: string) => void;
}

const FilterCategories = ({
  categories,
  selectedCategory,
  setSelectedCategory
}: FilterCategoriesProps) => {
  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {categories.map((category) => (
        <Button
          key={category}
          variant={selectedCategory === category ? "default" : "outline"}
          className={selectedCategory === category ? "bg-recrea-turquoise text-white" : ""}
          onClick={() => setSelectedCategory(category)}
        >
          {category}
        </Button>
      ))}
    </div>
  );
};

export default FilterCategories;
