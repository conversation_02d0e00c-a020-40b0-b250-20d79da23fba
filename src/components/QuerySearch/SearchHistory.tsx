
import React from 'react';
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover';
import { 
  Command, 
  CommandInput, 
  CommandList, 
  CommandEmpty, 
  CommandGroup, 
  CommandItem 
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { History, BookmarkCheck } from 'lucide-react';

interface SearchHistoryItem {
  query: string;
  timestamp: number;
}

interface SavedQuery {
  id: number;
  query: string;
}

interface SearchHistoryProps {
  showHistory: boolean;
  setShowHistory: (show: boolean) => void;
  searchHistory: SearchHistoryItem[];
  savedQueries: SavedQuery[];
  selectFromHistory: (item: SearchHistoryItem) => void;
  formatTime: (timestamp: number) => string;
}

const SearchHistory = ({
  showHistory,
  setShowHistory,
  searchHistory,
  savedQueries,
  selectFromHistory,
  formatTime
}: SearchHistoryProps) => {
  return (
    <Popover open={showHistory} onOpenChange={setShowHistory}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          className="py-6"
          disabled={searchHistory.length === 0}
        >
          <History size={20} />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Command>
          <CommandInput placeholder="Search history..." />
          <CommandList>
            <CommandEmpty>No search history found.</CommandEmpty>
            <CommandGroup heading="Recent Searches">
              {searchHistory.map((item, index) => (
                <CommandItem 
                  key={index} 
                  onSelect={() => selectFromHistory(item)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center">
                    <History size={16} className="mr-2 opacity-70" />
                    <span>{item.query}</span>
                  </div>
                  <span className="text-xs opacity-50">{formatTime(item.timestamp)}</span>
                </CommandItem>
              ))}
            </CommandGroup>
            {savedQueries.length > 0 && (
              <CommandGroup heading="Saved Queries">
                {savedQueries.map((item) => (
                  <CommandItem 
                    key={item.id} 
                    onSelect={() => selectFromHistory({ query: item.query, timestamp: Date.now() })}
                  >
                    <BookmarkCheck size={16} className="mr-2 text-recrea-turquoise" />
                    <span>{item.query}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default SearchHistory;
