
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search as SearchIcon, CheckSquare } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sector } from '@/hooks/useSectors';

interface SearchBarProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  handleSearch: () => void;
  isGranular: boolean;
  setIsGranular: (isGranular: boolean) => void;
  selectedSector: string;
  setSelectedSector: (sector: string) => void;
  sectors: Sector[] | undefined;
  sectorsLoading: boolean;
  inputRef: React.RefObject<HTMLInputElement>;
}

const SearchBar = ({
  searchQuery,
  setSearchQuery,
  handleSearch,
  isGranular,
  setIsGranular,
  selectedSector,
  setSelectedSector,
  sectors,
  sectorsLoading,
  inputRef
}: SearchBarProps) => {
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            ref={inputRef}
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Search for climate insights..."
            className="glass-input pr-10 py-6 text-lg"
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <Button 
              onClick={handleSearch} 
              variant="ghost" 
              size="icon"
              className="text-muted-foreground hover:text-primary"
            >
              <SearchIcon size={20} />
            </Button>
          </div>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="granular" 
            checked={isGranular}
            onCheckedChange={(checked) => setIsGranular(checked === true)}
          />
          <label
            htmlFor="granular"
            className="text-sm font-medium leading-none flex items-center gap-1 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            <CheckSquare size={16} className="mr-1" />
            Go Granular
          </label>
        </div>
        
        <div className="w-64">
          <Select 
            value={selectedSector} 
            onValueChange={setSelectedSector}
            disabled={sectorsLoading}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select technology sector" />
            </SelectTrigger>
            <SelectContent>
              {sectors && sectors.map((sector) => (
                <SelectItem key={sector.uuid} value={sector.name}>
                  {sector.name}
                </SelectItem>
              ))}
              {sectorsLoading && <SelectItem value="loading" disabled>Loading sectors...</SelectItem>}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default SearchBar;
