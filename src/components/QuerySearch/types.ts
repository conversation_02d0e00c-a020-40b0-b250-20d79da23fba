
export interface SearchHistoryItem {
  query: string;
  timestamp: number;
}

export interface SavedQuery {
  id: number;
  query: string;
}

export interface SearchResult {
  id: number;
  title: string;
  category: string;
  summary: string;
  content: string;
}

export interface SearchRequestPayload {
  query: string;
  search_type: "local" | "global";
  technology_sector: string;
  question_type: string;
}
