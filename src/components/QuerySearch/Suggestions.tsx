
import React from 'react';
import { Search as SearchIcon, Bookmark, BookmarkCheck } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SuggestionsProps {
  showSuggestions: boolean;
  filteredSuggestions: string[];
  selectSuggestion: (suggestion: string) => void;
  toggleSaveQuery: (query: string) => void;
  isQuerySaved: (query: string) => boolean;
}

const Suggestions = ({
  showSuggestions,
  filteredSuggestions,
  selectSuggestion,
  toggleSaveQuery,
  isQuerySaved
}: SuggestionsProps) => {
  if (!showSuggestions) return null;

  const handleSelectSuggestion = (suggestion: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Pass the entire suggestion text
    selectSuggestion(suggestion);
  };

  return (
    <div className="absolute z-10 w-full mt-1 bg-card rounded-lg border border-border shadow-lg">
      <ul className="py-1">
        {filteredSuggestions.map((suggestion, index) => (
          <li 
            key={index}
            className="px-4 py-2 hover:bg-accent cursor-pointer flex items-center justify-between"
            onClick={(e) => handleSelectSuggestion(suggestion, e)}
          >
            <div className="flex items-center">
              <SearchIcon size={14} className="mr-2 opacity-60" />
              <span>{suggestion}</span>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-6 w-6"
              onClick={(e) => {
                e.stopPropagation();
                toggleSaveQuery(suggestion);
              }}
            >
              {isQuerySaved(suggestion) ? (
                <BookmarkCheck size={14} className="text-recrea-turquoise" />
              ) : (
                <Bookmark size={14} />
              )}
            </Button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Suggestions;
