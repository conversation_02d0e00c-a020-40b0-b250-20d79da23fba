
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Search as SearchIcon } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Bookmark, BookmarkCheck, ChevronDown, ChevronUp } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface ResultItem {
  id: number;
  title: string;
  category: string;
  summary: string;
  content: string;
}

interface ResultsDisplayProps {
  isLoading: boolean;
  apiResponse: string | null;
  isJsonResponse: boolean;
  selectedCategory: string;
  searchResults: ResultItem[];
  viewFullResponse: (resultId: number) => void;
  toggleSaveQuery: (query: string) => void;
  isQuerySaved: (query: string) => boolean;
}

const ResultsDisplay = ({
  isLoading,
  apiResponse,
  isJsonResponse,
  selectedCategory,
  searchResults,
  viewFullResponse,
  toggleSaveQuery,
  isQuerySaved
}: ResultsDisplayProps) => {
  const [isJsonExpanded, setIsJsonExpanded] = useState(false);
  
  if (!apiResponse && searchResults.length === 0) return null;
  
  return (
    <>
      {apiResponse && (
        <div className="mb-8">
          <div className={`glass-card p-6 ${isLoading ? 'opacity-70' : ''}`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium">Search Results</h3>
              {isLoading && <div className="w-4 h-4 rounded-full border-2 border-t-transparent border-recrea-turquoise animate-spin"></div>}
            </div>
            
            {isJsonResponse ? (
              <Collapsible 
                open={isJsonExpanded} 
                onOpenChange={setIsJsonExpanded} 
                className="w-full"
              >
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm font-medium">JSON Response</div>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="p-0 h-8 w-8">
                      {isJsonExpanded ? (
                        <ChevronUp className="h-4 w-4" />
                      ) : (
                        <ChevronDown className="h-4 w-4" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                </div>
                <CollapsibleContent>
                  <div className="bg-card rounded-md p-4 overflow-auto max-h-96">
                    <pre className="text-sm whitespace-pre-wrap break-words">
                      <code>{apiResponse}</code>
                    </pre>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            ) : (
              <div className="prose prose-sm max-w-none dark:prose-invert">
                {apiResponse}
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="glass-card p-6">
        <h3 className="text-xl font-medium mb-4">
          Suggested Results {searchResults.length > 0 && `(${searchResults.length})`}
        </h3>
        
        {searchResults.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {searchResults.map((result) => (
              <Card key={result.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{result.title}</CardTitle>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 -mt-1 -mr-2"
                      onClick={() => toggleSaveQuery(result.title)}
                    >
                      {isQuerySaved(result.title) ? (
                        <BookmarkCheck size={16} className="text-recrea-turquoise" />
                      ) : (
                        <Bookmark size={16} />
                      )}
                    </Button>
                  </div>
                  <div className="inline-block px-2 py-1 text-xs rounded-full bg-recrea-light-mint text-recrea-dark dark:bg-recrea-card-bg dark:text-white">
                    {result.category}
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm line-clamp-3">
                    {result.summary}
                  </CardDescription>
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="ml-auto flex items-center gap-1 text-xs"
                    onClick={() => viewFullResponse(result.id)}
                  >
                    View More
                    <SearchIcon size={14} />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center p-8">
            <div className="rounded-full bg-recrea-light-mint w-16 h-16 mx-auto flex items-center justify-center mb-4">
              <SearchIcon size={24} className="text-recrea-turquoise" />
            </div>
            <h4 className="text-lg font-medium mb-2">No suggested results found</h4>
            <p className="text-muted-foreground">Try a different query or adjust your filters</p>
          </div>
        )}
      </div>
    </>
  );
};

export default ResultsDisplay;
