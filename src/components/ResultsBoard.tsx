import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  Calendar, 
  Filter, 
  Download, 
  Trash2,
  Play,
  Eye,
  DollarSign,
  Leaf,
  Zap,
  Clock
} from 'lucide-react';

interface OptimizationMetrics {
  carbonReduction: number;
  costSavings: number;
  energyEfficiency: number;
  implementationTime: number;
  riskLevel: 'Low' | 'Medium' | 'High';
  feasibilityScore: number;
}

interface OptimizationResult {
  id: string;
  name: string;
  baselineScenario: string;
  optimizedScenario: string;
  metrics: OptimizationMetrics;
  status: 'running' | 'completed' | 'failed';
  progress: number;
  createdAt: string;
  completedAt?: string;
  recommendations: string[];
  keyInsights: string[];
}

interface ResultsBoardProps {
  onSelectResult?: (result: OptimizationResult) => void;
  onNewOptimization?: () => void;
}

export const ResultsBoard: React.FC<ResultsBoardProps> = ({
  onSelectResult,
  onNewOptimization
}) => {
  const [results, setResults] = useState<OptimizationResult[]>([]);
  const [selectedResult, setSelectedResult] = useState<OptimizationResult | null>(null);
  const [filter, setFilter] = useState<'all' | 'recent' | 'high-impact'>('all');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data initialization
  useEffect(() => {
    const mockResults: OptimizationResult[] = [
      {
        id: '1',
        name: 'Steel Production Optimization',
        baselineScenario: 'Current Steel Process',
        optimizedScenario: 'Efficient Steel Process',
        metrics: {
          carbonReduction: 25.5,
          costSavings: 180000,
          energyEfficiency: 18.2,
          implementationTime: 12,
          riskLevel: 'Medium',
          feasibilityScore: 85
        },
        status: 'completed',
        progress: 100,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        recommendations: [
          'Implement heat recovery systems',
          'Upgrade to electric arc furnaces',
          'Optimize material flow processes'
        ],
        keyInsights: [
          'Major savings from energy efficiency improvements',
          'Carbon reduction primarily from process optimization'
        ]
      },
      {
        id: '2',
        name: 'Chemical Process Enhancement',
        baselineScenario: 'Standard Chemical Process',
        optimizedScenario: 'Green Chemical Process',
        metrics: {
          carbonReduction: 32.1,
          costSavings: 245000,
          energyEfficiency: 22.8,
          implementationTime: 18,
          riskLevel: 'High',
          feasibilityScore: 78
        },
        status: 'completed',
        progress: 100,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        completedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        recommendations: [
          'Transition to renewable energy sources',
          'Implement catalytic process improvements',
          'Install advanced monitoring systems'
        ],
        keyInsights: [
          'High impact potential with significant investment required',
          'Technology risks need careful management'
        ]
      }
    ];
    setResults(mockResults);
    setSelectedResult(mockResults[0]);
  }, []);

  const filteredResults = results.filter(result => {
    switch (filter) {
      case 'recent':
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        return new Date(result.createdAt) > weekAgo;
      case 'high-impact':
        return result.metrics.carbonReduction > 20 || result.metrics.costSavings > 150000;
      default:
        return true;
    }
  });

  const handleResultSelect = (result: OptimizationResult) => {
    setSelectedResult(result);
    if (onSelectResult) {
      onSelectResult(result);
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'High': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="h-full flex gap-6">
      {/* Results List */}
      <div className="w-1/3 space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Optimization Results</h2>
          <Button onClick={onNewOptimization} size="sm">
            <Play size={16} className="mr-2" />
            New
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <Filter size={16} />
          <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Results</SelectItem>
              <SelectItem value="recent">Recent (7 days)</SelectItem>
              <SelectItem value="high-impact">High Impact</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto">
          {filteredResults.map((result) => (
            <Card 
              key={result.id} 
              className={`cursor-pointer transition-colors ${
                selectedResult?.id === result.id ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'
              }`}
              onClick={() => handleResultSelect(result)}
            >
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-sm font-medium">{result.name}</CardTitle>
                  <Badge className={getRiskColor(result.metrics.riskLevel)}>
                    {result.metrics.riskLevel}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <Leaf size={12} className="text-green-500" />
                    <span>{result.metrics.carbonReduction.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <DollarSign size={12} className="text-blue-500" />
                    <span>${(result.metrics.costSavings / 1000).toFixed(0)}k</span>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {new Date(result.createdAt).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Detailed View */}
      <div className="flex-1">
        {selectedResult ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-semibold">{selectedResult.name}</h2>
                <p className="text-muted-foreground">
                  {selectedResult.optimizedScenario} vs {selectedResult.baselineScenario}
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download size={16} className="mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Trash2 size={16} className="mr-2" />
                  Delete
                </Button>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="metrics">Metrics</TabsTrigger>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                <TabsTrigger value="insights">Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-1">
                        <Leaf size={16} className="text-green-500" />
                        Carbon Reduction
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {selectedResult.metrics.carbonReduction.toFixed(1)}%
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-1">
                        <DollarSign size={16} className="text-blue-500" />
                        Cost Savings
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">
                        {formatCurrency(selectedResult.metrics.costSavings)}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-1">
                        <Zap size={16} className="text-yellow-500" />
                        Energy Efficiency
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-yellow-600">
                        +{selectedResult.metrics.energyEfficiency.toFixed(1)}%
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-1">
                        <Clock size={16} className="text-purple-500" />
                        Implementation
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-purple-600">
                        {selectedResult.metrics.implementationTime} months
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Project Timeline</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Created</span>
                        <span>{new Date(selectedResult.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Completed</span>
                        <span>{selectedResult.completedAt ? new Date(selectedResult.completedAt).toLocaleDateString() : 'In Progress'}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Status</span>
                        <Badge variant={selectedResult.status === 'completed' ? 'default' : 'secondary'}>
                          {selectedResult.status}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="metrics" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Detailed Metrics Comparison</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center text-muted-foreground">
                        <BarChart3 size={48} className="mx-auto mb-2" />
                        <p>Detailed metrics visualization would be implemented here</p>
                        <p className="text-sm">Charts showing before/after comparisons</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="recommendations" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Recommended Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedResult.recommendations.map((recommendation, index) => (
                        <div key={index} className="flex items-start gap-2 p-3 bg-green-50 rounded-lg">
                          <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </div>
                          <span className="text-sm">{recommendation}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="insights" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Key Insights</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedResult.keyInsights.map((insight, index) => (
                        <div key={index} className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                          <TrendingUp size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{insight}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-muted-foreground">
              <Eye size={48} className="mx-auto mb-4" />
              <p>Select a result to view details</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
