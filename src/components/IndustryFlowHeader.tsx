import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface IndustryFlowHeaderProps {
  sectorName: string;
  flowTypeLabel: string;
  scenarioName?: string;
  isScenarioMode?: boolean;
  // Navigation props
  onExitScenario?: () => void;
}

export const IndustryFlowHeader: React.FC<IndustryFlowHeaderProps> = ({
  sectorName,
  flowTypeLabel,
  scenarioName,
  isScenarioMode = false,
  onExitScenario
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-between pt-8 pb-4 px-12 border-b border-gray-100" style={{ minHeight: 72 }}>
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => {
            // If in scenario mode and onExitScenario is provided, use it to return to inventory
            if (isScenarioMode && onExitScenario) {
              onExitScenario();
            } else {
              // Otherwise, navigate to optimizer home
              navigate('/optimizer');
            }
          }}
          className="hover:bg-gray-100 rounded-lg"
        >
          <span className="sr-only">Back</span>
          <ArrowLeft size={20} className="text-gray-600" />
        </Button>
        <div className="flex flex-col">
          <div className="text-sm text-gray-500 font-medium uppercase tracking-wide">
            {sectorName} Sector
          </div>
          <div className="text-xl font-bold text-gray-900" style={{ letterSpacing: '-0.5px' }}>
            {scenarioName || flowTypeLabel}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <div className={`px-3 py-1.5 rounded-full text-sm font-medium ${
          isScenarioMode
            ? 'bg-blue-100 text-blue-700 border border-blue-200'
            : 'bg-green-100 text-green-700 border border-green-200'
        }`}>
          {isScenarioMode ? 'Scenario' : 'Inventory'}
        </div>
      </div>
    </div>
  );
};
