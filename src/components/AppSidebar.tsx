import React, { useContext } from "react";
import { useLocation, useParams, useNavigate } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { FileText, Book, Link, ArrowLeft, LayoutGrid, BarChart, Settings, LifeBuoy, ChevronRight, User } from "lucide-react";
import { SectorActivitiesContext } from '@/contexts/SectorActivitiesContext';
import marketplaceData from '@/data/marketplaceData';
import experts from '@/data/expertiseData';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Check, ChevronDown } from 'lucide-react';

const navItems = [
  {
    title: "Document",
    url: "/document-chat",
    icon: FileText,
    group: "search",
  },
  {
    title: "Corpus",
    url: "/query-search",
    icon: Book,
    group: "search",
  },
  {
    title: "Notes",
    url: "/notes-creator",
    icon: FileText,
    group: "notes",
  }
];

const suggestedLinks = [
  {
    title: "Research Paper",
    url: "#",
  },
  {
    title: "Industry Report",
    url: "#",
  },
  {
    title: "Case Study",
    url: "#",
  },
  {
    title: "External Resource",
    url: "#",
  }
];

// Extract unique expertise names from experts data
const uniqueExpertise = Array.from(new Set(
  experts.flatMap(expert => expert.expertise)
)).sort();

const AppSidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    sectors,
    loadingSectors,
    selectedSector,
    activities,
    loadingActivities,
    activitiesError
  } = useContext(SectorActivitiesContext);

  const isDocumentChatPage = location.pathname === "/document-chat";
  const isOptimizerContext = location.pathname.startsWith('/optimizer') || location.pathname.startsWith('/industry-flow');
  const isMarketplaceContext = location.pathname.startsWith('/marketplace');

  return (
    <Sidebar>
      <SidebarHeader className="p-4 flex items-center justify-center">
        <div className="w-10 h-10 rounded-full bg-gradient-to-br from-recrea-turquoise to-recrea-teal flex items-center justify-center text-white font-bold">
          AI
        </div>
      </SidebarHeader>
      <SidebarContent>
        {isMarketplaceContext ? (
          <>
            {/* Activities Section */}
            <SidebarGroup>
              <div className="text-sm font-semibold px-2 mb-1">Activities</div>
              <SidebarGroupContent>
                <SidebarMenu>
                  {loadingActivities || loadingSectors ? (
                    // Skeleton loader for activities
                    Array.from({ length: 4 }).map((_, idx) => (
                      <SidebarMenuItem key={idx}>
                        <div className="h-8 bg-muted animate-pulse rounded-md w-5/6 mx-auto my-2" />
                      </SidebarMenuItem>
                    ))
                  ) : activitiesError ? (
                    <SidebarMenuItem>
                      <div className="text-red-500 text-xs px-4 py-2">{activitiesError}</div>
                    </SidebarMenuItem>
                  ) : activities?.length === 0 ? (
                    <SidebarMenuItem>
                      <div className="text-xs px-4 py-2">No activities found for this sector.</div>
                    </SidebarMenuItem>
                  ) : (
                    activities?.map((activity) => {
                      const url = `/marketplace/${activity.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}?sector=${selectedSector}`;
                      return (
                        <SidebarMenuItem key={activity.uuid}>
                          <SidebarMenuButton
                            asChild
                            isActive={location.pathname + location.search === url}
                            tooltip={activity.name}
                            className={
                              location.pathname + location.search === url
                                ? "bg-recrea-turquoise/10 text-recrea-turquoise"
                                : "hover:bg-gray-50"
                            }
                          >
                            <a href={url} className="flex items-center gap-2 px-4 min-h-[40px]">
                              <span className="text-sm text-left whitespace-normal flex-1">{activity.name}</span>
                            </a>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      );
                    })
                  )}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>

            {/* Expertise Section */}
            <SidebarGroup>
              <div className="text-sm font-semibold px-2 mb-1">Expertise</div>
              <SidebarGroupContent className="max-h-[400px] overflow-auto group-data-[collapsible=icon]:overflow-hidden">
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      className="hover:bg-gray-50"
                    >
                      <a href={`/marketplace?expertise=regulations`} className="flex items-center gap-3 px-4">
                        <span className="text-sm">Regulations and Policies</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </>
        ) : (
          <>
            {/* Only show Search group if not in Optimizer context */}
            {!isOptimizerContext && (
              <SidebarGroup>
                <div className="text-sm font-semibold px-2 mb-1">Search</div>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {navItems
                      .filter((item) => item.group === "search")
                      .map((item) => (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton
                            asChild
                            isActive={location.pathname === item.url}
                            tooltip={item.title}
                            className={
                              location.pathname === item.url
                                ? "bg-recrea-turquoise/10 text-recrea-turquoise border-l-2 border-recrea-turquoise"
                                : ""
                            }
                          >
                            <a href={item.url} className="flex items-center gap-3 px-4">
                              <item.icon className="h-5 w-5" />
                              <span className="font-medium">{item.title}</span>
                            </a>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}

            {/* Conditional Suggested Documents & Links section - only show if not in Optimizer context */}
            {isDocumentChatPage && !isOptimizerContext && (
              <SidebarGroup>
                <div className="text-sm font-semibold px-2 mb-1">Suggested Documents & Links</div>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {suggestedLinks.map((link) => (
                      <SidebarMenuItem key={link.title}>
                        <SidebarMenuButton
                          asChild
                          tooltip={link.title}
                        >
                          <a href={link.url} className="flex items-center gap-3 px-4">
                            <Link className="h-5 w-5" />
                            <span className="font-medium">{link.title}</span>
                          </a>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}

            {/* Notes: just the item, no group label - only show if not in Optimizer context */}
            {!isOptimizerContext && (
              <SidebarGroup>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {navItems
                      .filter((item) => item.group === "notes")
                      .map((item) => (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton
                            asChild
                            isActive={location.pathname === item.url}
                            tooltip={item.title}
                            className={
                              location.pathname === item.url
                                ? "bg-recrea-turquoise/10 text-recrea-turquoise border-l-2 border-recrea-turquoise"
                                : ""
                            }
                          >
                            <a href={item.url} className="flex items-center gap-3 px-4">
                              <item.icon className="h-5 w-5" />
                              <span className="font-medium">{item.title}</span>
                            </a>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            )}
          </>
        )}
      </SidebarContent>
      <SidebarFooter className="p-4 border-t">
        <div className="text-xs text-center text-muted-foreground">
          © 2025 AI Document Analysis
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
