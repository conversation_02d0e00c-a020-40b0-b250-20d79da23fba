
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { FolderPlus, Link2, <PERSON>pu, ShoppingBag } from 'lucide-react';
import { FlowModeType } from "@/components/FlowModeDialog";

interface IndustryFlowNodeActionsProps {
  selectedNode: any;
  handleFlowAction: (type: FlowModeType, node: any) => void;
  handleRename: () => void; // Keeping the prop for compatibility
  handleTechnologySelect?: (nodeId: string, technologyId: string) => void;
  onOpenConnectionForm: (sourceNode: any) => void;
  onMarketplaceClick?: (nodeId: string) => void; // New prop for marketplace action
}

export const IndustryFlowNodeActions: React.FC<IndustryFlowNodeActionsProps> = ({
  selectedNode,
  handleFlowAction,
  onOpenConnectionForm,
  onMarketplaceClick
}) => {
  if (!selectedNode) return null;

  // Handler for marketplace button click
  const handleMarketplaceClick = () => {
    if (onMarketplaceClick && selectedNode) {
      onMarketplaceClick(selectedNode.id);
    } else {
      console.log("Marketplace clicked for node:", selectedNode.id);
    }
  };

  return (
    <div
      className="fixed bottom-0 left-0 w-full bg-white border-t z-30 shadow-[0_-2px_24px_#926efb0f] flex items-center justify-center py-5 gap-5"
      style={{ animation: 'fadeUp 0.18s' }}
      onClick={e => e.stopPropagation()}
    >
      <Button
        variant="outline"
        onClick={() => handleFlowAction("subflow", selectedNode)}
        className="flex items-center gap-2 border-2 border-[#d3e4fd] bg-[#eaf4ff] text-[#4062a7] font-semibold px-8 py-4 text-base rounded-xl hover:bg-[#d3e4fd]"
      >
        <FolderPlus size={20} />
        Create Subflow
      </Button>
      <Button
        variant="outline"
        onClick={() => handleFlowAction("byproduct", selectedNode)}
        className="flex items-center gap-2 border-2 border-[#ffdee2] bg-[#fff1f3] text-[#b46d8f] font-semibold px-8 py-4 text-base rounded-xl hover:bg-[#ffeeef]"
      >
        <Link2 size={20} />
        Create Byproduct Flow
      </Button>
      <Button
        variant="outline"
        onClick={() => onOpenConnectionForm(selectedNode)}
        className="flex items-center gap-2 border-2 border-[#d3fdde] bg-[#eafff4] text-[#40a762] font-semibold px-8 py-4 text-base rounded-xl hover:bg-[#d3fde4]"
      >
        <Cpu size={18} />
        Technology
      </Button>
      <Button
        variant="outline"
        onClick={handleMarketplaceClick}
        className="flex items-center gap-2 border-2 border-[#fdefd3] bg-[#fff8ea] text-[#a77640] font-semibold px-8 py-4 text-base rounded-xl hover:bg-[#fde4d3]"
      >
        <ShoppingBag size={18} />
        Marketplace
      </Button>
    </div>
  );
};
