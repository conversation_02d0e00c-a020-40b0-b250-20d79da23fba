
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";

interface DetailedTopicModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  sector: string;
  description?: string;
}

const DetailedTopicModal: React.FC<DetailedTopicModalProps> = ({
  isOpen,
  onClose,
  title,
  sector,
  description
}) => {
  const [detailedExplanation, setDetailedExplanation] = useState<string>("");
  const [followUpQuestion, setFollowUpQuestion] = useState<string>("");
  const [followUpAnswer, setFollowUpAnswer] = useState<string>("");
  const [isLoadingExplanation, setIsLoadingExplanation] = useState<boolean>(false);
  const [isLoadingFollowUp, setIsLoadingFollowUp] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch the detailed explanation when the modal opens
  useEffect(() => {
    if (isOpen && title && sector) {
      fetchDetailedExplanation();
    }
    
    // Reset states when modal closes
    return () => {
      if (!isOpen) {
        setFollowUpQuestion("");
        setFollowUpAnswer("");
        setError(null);
      }
    };
  }, [isOpen, title, sector]);

  const fetchDetailedExplanation = async () => {
    setIsLoadingExplanation(true);
    setError(null);
    
    try {
      console.log("Fetching detailed explanation for:", title, "in sector:", sector);
      const accessToken = localStorage.getItem('accessToken');
      
      if (!accessToken) {
        throw new Error('No access token found. Please log in again.');
      }
      
      const initialQuery = `Provide a comprehensive, well-structured explanation of the following topic:\n\n"${title}"\n\nin the context of the "${sector}" industry. Cover:\n- Its relevance and implications for the sector\n- Any recent developments, context, or key issues\n- Why it matters for companies in this space\n\nResponse must be 150–250 words, grounded in corpus data, without generic or unrelated info.\n\nReturn plain text only.`;
      
      const response = await fetch('https://api.recre8.earth/ask', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: initialQuery,
          search_type: "global",
          technology_sector: sector,
          question_type: "Default"
        })
      });
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log("API Response:", data);
      
      // Handle different response formats from the API
      let explanationText = "";
      if (typeof data.response === 'string') {
        explanationText = data.response;
      } else if (data.response && typeof data.response === 'object') {
        // If response is an object, try to extract text content
        explanationText = data.response.answer || data.response.text || JSON.stringify(data.response);
      } else if (data.answer) {
        explanationText = data.answer;
      } else {
        throw new Error("Unexpected response format");
      }
      
      setDetailedExplanation(explanationText);
    } catch (err: any) {
      console.error("Error fetching detailed explanation:", err);
      setError("Failed to load detailed information for this topic.");
      toast({
        title: "Error",
        description: err.message || "Failed to load detailed information",
        variant: "destructive"
      });
    } finally {
      setIsLoadingExplanation(false);
    }
  };
  
  const handleFollowUpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!followUpQuestion.trim()) {
      toast({
        title: "Missing question",
        description: "Please enter a question to continue",
      });
      return;
    }
    
    setIsLoadingFollowUp(true);
    setFollowUpAnswer("");
    
    try {
      console.log("Submitting follow-up question:", followUpQuestion);
      const accessToken = localStorage.getItem('accessToken');
      
      if (!accessToken) {
        throw new Error('No access token found. Please log in again.');
      }
      
      const response = await fetch('https://api.recre8.earth/ask', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: followUpQuestion,
          search_type: "global",
          technology_sector: sector,
          question_type: "Default"
        })
      });
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log("Follow-up API Response:", data);
      
      // Handle different response formats from the API
      let answerText = "";
      if (typeof data.response === 'string') {
        answerText = data.response;
      } else if (data.response && typeof data.response === 'object') {
        // If response is an object, try to extract text content
        answerText = data.response.answer || data.response.text || JSON.stringify(data.response);
      } else if (data.answer) {
        answerText = data.answer;
      } else {
        throw new Error("Unexpected response format");
      }
      
      setFollowUpAnswer(answerText);
    } catch (err: any) {
      console.error("Error with follow-up question:", err);
      toast({
        title: "Error",
        description: err.message || "Failed to get answer to your question",
        variant: "destructive"
      });
      setFollowUpAnswer("Sorry, we couldn't process your question at the moment. Please try again later.");
    } finally {
      setIsLoadingFollowUp(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </DialogHeader>
        
        {/* Initial explanation section */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3 text-recrea-turquoise">Topic Overview</h3>
          
          {isLoadingExplanation ? (
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-[95%]" />
              <Skeleton className="h-4 w-[90%]" />
              <Skeleton className="h-4 w-[97%]" />
              <Skeleton className="h-4 w-[85%]" />
            </div>
          ) : error ? (
            <div className="p-4 border border-destructive/50 rounded-md bg-destructive/10">
              <p className="text-destructive">{error}</p>
              <p className="text-sm text-muted-foreground mt-1">
                Please try again or contact support if this issue persists.
              </p>
            </div>
          ) : (
            <div className="prose dark:prose-invert max-w-none">
              {detailedExplanation ? (
                <p className="whitespace-pre-wrap">{detailedExplanation}</p>
              ) : (
                <p className="text-muted-foreground italic">
                  Detailed explanation unavailable at the moment.
                </p>
              )}
            </div>
          )}
        </div>
        
        {/* Follow-up question section */}
        <div>
          <h3 className="text-lg font-medium mb-3 text-recrea-turquoise">Ask a Follow-Up Question</h3>
          <form onSubmit={handleFollowUpSubmit} className="space-y-4">
            <div className="flex gap-2">
              <Input 
                value={followUpQuestion}
                onChange={(e) => setFollowUpQuestion(e.target.value)}
                placeholder="Ask a question about this topic..."
                className="flex-1"
              />
              <Button type="submit" disabled={isLoadingFollowUp}>
                {isLoadingFollowUp ? "Sending..." : "Ask"}
              </Button>
            </div>
            
            {/* Follow-up answer section */}
            {(followUpAnswer || isLoadingFollowUp) && (
              <div className="mt-4 border rounded-md p-4">
                <h4 className="font-medium mb-2">Answer</h4>
                {isLoadingFollowUp ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-[90%]" />
                    <Skeleton className="h-4 w-[95%]" />
                  </div>
                ) : (
                  <p className="text-sm whitespace-pre-wrap">{followUpAnswer}</p>
                )}
              </div>
            )}
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DetailedTopicModal;
