import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>heck, CircleDot, CircleMinus, AlertCircle } from 'lucide-react';
import { fetchMaterialTopicsData, MaterialTopic as MaterialTopicType } from '@/hooks/useMaterialTopics';
import { useToast } from "@/hooks/use-toast";
import { Button } from '@/components/ui/button';
import { apiCache, generateCacheKey } from '@/utils/apiCache';
import FetchingIndicator from './shared/FetchingIndicator';

interface MaterialTopicsTabProps {
  selectedSector: string;
  sectorOptions: Array<{ value: string; label: string; }>;
  materialTopicsData?: Array<{
    id?: number;
    title: string;
    description: string;
    relevance: string;
    icon?: React.ReactNode;
  }>;
  isLoading?: boolean;
  error?: string | null;
}

const MaterialTopicsTab: React.FC<MaterialTopicsTabProps> = ({
  selectedSector,
  sectorOptions,
  materialTopicsData: cachedTopicsData,
  isLoading: isLoadingProp,
  error: errorProp,
}) => {
  const navigate = useNavigate();
  const [topicsData, setTopicsData] = useState<MaterialTopicType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [initialLoad, setInitialLoad] = useState(true);
  const { toast } = useToast();
  const lastLoadedSectorRef = React.useRef<string>("");

  // Get the correctly formatted sector name for the API
  const getFormattedSectorName = (): string => {
    const selectedOption = sectorOptions.find(s => s.value === selectedSector);
    return selectedOption?.label || "";
  };
  
  const formatSector = getFormattedSectorName();
  const cacheKey = generateCacheKey('materialTopics', { sector: formatSector });

  // Function to load material topics data
  const loadMaterialTopics = async (sectorName: string, forceRefresh = false) => {
    if (!sectorName) {
      console.warn("MaterialTopicsTab: No valid sector name provided");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      let data: MaterialTopicType[];
      
      // Use cache if available and not forcing refresh
      if (!forceRefresh && apiCache.has(cacheKey)) {
        data = apiCache.get<MaterialTopicType[]>(cacheKey) || [];
        console.log(`MaterialTopicsTab: Using cached data for ${sectorName}`, data);
      } else {
        console.log(`MaterialTopicsTab: Fetching material topics for sector: ${sectorName}`);
        data = await fetchMaterialTopicsData(sectorName);
        
        // Cache the successful response
        if (data && data.length > 0) {
          apiCache.set(cacheKey, data);
        }
      }
      
      if (data && data.length > 0) {
        setTopicsData(data);
        lastLoadedSectorRef.current = sectorName;
      } else {
        console.warn("MaterialTopicsTab: Empty or invalid material topics data received");
        setTopicsData([]);
        
        // Only set error if we got no data at all
        if (!data) {
          setError("No material topics data available for this sector");
        }
      }
    } catch (err: any) {
      console.error("MaterialTopicsTab: Error loading material topics:", err);
      setError(err.message || 'Failed to load material topics data');
      toast({
        title: 'Error fetching material topics',
        description: err.message || 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setInitialLoad(false);
      console.log("MaterialTopicsTab: Finished loading material topics");
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    const sectorName = getFormattedSectorName();
    setRetryCount(prev => prev + 1);
    apiCache.remove(cacheKey); // Remove from cache to force refresh
    loadMaterialTopics(sectorName, true);
  };

  // Always fetch when sector changes or on retry
  useEffect(() => {
    const sectorName = getFormattedSectorName();
    if (sectorName) {
      loadMaterialTopics(sectorName);
    }
    
    return () => {
      // Cleanup - nothing specific needed here
    };
  }, [selectedSector, retryCount, sectorOptions]);

  // Use either cached data from props or freshly fetched data
  // Prioritize the fresh data if available
  const topicsToDisplay = topicsData.length > 0 ? topicsData : [];
  
  // Determine loading state - use prop if provided, otherwise use local state
  const isLoading = isLoadingProp !== undefined ? isLoadingProp : loading;
  
  // Determine error state - use prop if provided, otherwise use local state
  const displayError = errorProp || error;
  
  // Determine if we should show the loading indicator
  const showLoadingIndicator = initialLoad || isLoading;

  // Get the icon based on topic title and relevance
  const getTopicIcon = (topic: MaterialTopicType) => {
    // Base icon on relevance
    switch (topic.relevance) {
      case "High":
        return <CircleCheck className="h-5 w-5 text-recrea-turquoise" />;
      case "Medium":
        return <CircleDot className="h-5 w-5 text-recrea-turquoise" />;
      case "Low":
        return <CircleMinus className="h-5 w-5 text-recrea-turquoise" />;
      default:
        return <AlertCircle className="h-5 w-5 text-recrea-turquoise" />;
    }
  };

  // Loading state with spinner
  if (showLoadingIndicator) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Material Topics for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <p className="text-muted-foreground mb-6">
          Key sustainability issues that are most relevant to your industry and operations.
        </p>
        
        <FetchingIndicator message="Fetching Material Topics..." />
      </div>
    );
  }

  // Error state
  if (displayError) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Material Topics for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12 text-destructive">
          <AlertCircle className="w-12 h-12 mb-4" />
          <p className="text-lg font-medium">{displayError}</p>
          <p className="text-muted-foreground mt-2 mb-4">Please try again later or contact support if the issue persists.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  // Empty state
  if (!showLoadingIndicator && topicsToDisplay.length === 0) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Material Topics for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12">
          <AlertCircle className="w-12 h-12 mb-4 text-muted-foreground" />
          <p className="text-lg font-medium">No Material Topics Found</p>
          <p className="text-muted-foreground mt-2 mb-4">We couldn't find any material topics for this sector. Please try another sector or contact support.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  const handleTopicClick = (topic: MaterialTopicType, index: number) => {
    const topicId = topic.id?.toString() || index.toString();
    navigate(`/dashboard/material-topics/${topicId}`, {
      state: { topic: { ...topic, id: topicId } }
    });
  };

  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
        Material Topics for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
      </h2>
      
      <p className="text-muted-foreground mb-6">
        Key sustainability issues that are most relevant to your industry and operations.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {topicsToDisplay.map((topic, index) => (
          <Card 
            key={`topic-${topic.id || index}`} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleTopicClick(topic, index)}
          >
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                {topic.icon || getTopicIcon(topic)}
                <CardTitle className="text-lg">{topic.title}</CardTitle>
              </div>
              <Badge variant={topic.relevance === "High" ? "destructive" : "outline"} className="mt-1">
                {topic.relevance} Relevance
              </Badge>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {topic.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default MaterialTopicsTab;
