import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { ClimateRisk, fetchClimateRisks } from '@/utils/searchApi';
import { AlertCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { apiCache, generateCacheKey } from '@/utils/apiCache';
import FetchingIndicator from './shared/FetchingIndicator';

interface ClimateRisksTabProps {
  selectedSector: string;
  sectorOptions: Array<{ value: string; label: string; }>;
  climateRisksData?: ClimateRisk[];
  isLoading?: boolean;
  error?: string | null;
}

const ClimateRisksTab: React.FC<ClimateRisksTabProps> = ({
  selectedSector,
  sectorOptions,
  climateRisksData: cachedData,
  isLoading: isLoadingProp,
  error: errorProp
}) => {
  const navigate = useNavigate();
  const [climateRisksData, setClimateRisksData] = useState<ClimateRisk[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [initialLoad, setInitialLoad] = useState(true);
  const { toast, dismiss, toasts } = useToast();
  const lastLoadedSectorRef = React.useRef<string>("");

  // Get the correctly formatted sector name for the API
  const getFormattedSectorName = (): string => {
    const selectedOption = sectorOptions.find(s => s.value === selectedSector);
    return selectedOption?.label || "";
  };

  const formatSector = getFormattedSectorName();
  const cacheKey = generateCacheKey('climateRisks', { sector: formatSector });

  // Function to load climate risks with caching
  const loadClimateRisks = async (sectorName: string, forceRefresh = false) => {
    if (!sectorName) {
      console.warn("ClimateRisksTab: No valid sector name provided");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      let risksData: ClimateRisk[] | null;
      
      // Use cache if available and not forcing refresh
      if (!forceRefresh && apiCache.has(cacheKey)) {
        risksData = apiCache.get<ClimateRisk[]>(cacheKey);
        console.log(`ClimateRisksTab: Using cached data for ${sectorName}`, risksData);
      } else {
        console.log(`ClimateRisksTab: Fetching climate risks for sector: ${sectorName}`);
        risksData = await fetchClimateRisks(sectorName, { toast, dismiss, toasts });
        
        // Cache the successful response
        if (risksData && risksData.length > 0) {
          apiCache.set(cacheKey, risksData);
        }
      }
      
      if (risksData && risksData.length > 0) {
        setClimateRisksData(risksData);
        lastLoadedSectorRef.current = sectorName;
      } else {
        console.warn("ClimateRisksTab: Empty or invalid climate risks data received");
        setClimateRisksData([]);
        
        // Only set error if we got no data at all
        if (!risksData) {
          setError("No climate risks data available for this sector");
        }
      }
    } catch (err: any) {
      console.error("ClimateRisksTab: Error loading climate risks:", err);
      setError(err.message || "Failed to load climate risks data");
      toast({
        title: 'Error fetching climate risks',
        description: err.message || 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setInitialLoad(false);
      console.log("ClimateRisksTab: Finished loading climate risks");
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    const sectorName = getFormattedSectorName();
    setRetryCount(prev => prev + 1);
    apiCache.remove(cacheKey); // Remove from cache to force refresh
    loadClimateRisks(sectorName, true);
  };

  // Always fetch when sector changes or on retry
  useEffect(() => {
    const sectorName = getFormattedSectorName();
    if (sectorName) {
      loadClimateRisks(sectorName);
    }
    
    return () => {
      // Cleanup - nothing specific needed here
    };
  }, [selectedSector, retryCount, sectorOptions]);

  // Use fresh data if available, otherwise fall back to cached data
  const displayData = climateRisksData.length > 0 ? climateRisksData : [];
  const isLoading = isLoadingProp !== undefined ? isLoadingProp : loading;
  const displayError = errorProp || error;
  const showLoadingIndicator = initialLoad || isLoading;

  // Helper function to determine badge styling based on relevance/severity/impact_level
  const getBadgeStyle = (level?: string) => {
    if (!level) return "bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-white";
    
    switch(level.toLowerCase()) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100";
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100";
      case "low":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
      default:
        return "bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-white";
    }
  };

  // Helper function to determine badge variant based on relevance/severity/impact_level
  const getBadgeVariant = (level?: string) => {
    if (!level) return "outline";
    
    switch(level.toLowerCase()) {
      case "high":
        return "destructive";
      case "medium":
        return "outline";
      case "low":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Loading state with spinner
  if (showLoadingIndicator) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Climate Risks for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <p className="text-muted-foreground mb-6">
          Physical and transitional climate-related risks that could impact your operations.
        </p>
        
        <FetchingIndicator message="Fetching Climate Risks..." />
      </div>
    );
  }

  // Error state
  if (displayError) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Climate Risks for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12 text-destructive">
          <AlertCircle className="w-12 h-12 mb-4" />
          <p className="text-lg font-medium">{displayError}</p>
          <p className="text-muted-foreground mt-2 mb-4">Please try again later or contact support if the issue persists.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  // Empty state
  if (displayData.length === 0) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Climate Risks for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12">
          <FileText className="w-12 h-12 mb-4 text-muted-foreground" />
          <p className="text-lg font-medium">No Climate Risks Found</p>
          <p className="text-muted-foreground mt-2 mb-4">We couldn't find any climate risks for this sector. Please try another sector or contact support.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  const handleRiskClick = (risk: ClimateRisk) => {
    const riskId = risk.id?.toString() || risk.title.toLowerCase().replace(/\s+/g, '-');
    navigate(`/dashboard/climate-risks/${riskId}`, {
      state: { topic: { ...risk, id: riskId } }
    });
  };

  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
        Climate Risks for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
      </h2>
      
      <p className="text-muted-foreground mb-6">
        Physical and transitional climate-related risks that could impact your operations.
      </p>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {displayData.map(risk => (
          <Card 
            key={risk.id} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleRiskClick(risk)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{risk.title}</CardTitle>
                <div className="space-x-1">
                  {risk.category && (
                    <Badge className={getBadgeStyle(risk.category)}>
                      {risk.category}
                    </Badge>
                  )}
                  {(risk.severity || risk.relevance || risk.impact_level) && (
                    <Badge variant={getBadgeVariant(risk.severity || risk.relevance || risk.impact_level)}>
                      {risk.severity || risk.relevance || risk.impact_level}
                    </Badge>
                  )}
                </div>
              </div>
              {risk.type && (
                <div className="text-sm text-muted-foreground mt-1">
                  {risk.type}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-3">
                {risk.description}
              </p>
              
              {risk.impacts && risk.impacts.length > 0 && (
                <div className="mt-3">
                  <p className="text-sm font-medium mb-2">Key Impacts:</p>
                  <ul className="text-sm list-disc pl-5 space-y-1">
                    {risk.impacts.map((impact, index) => (
                      <li key={index}>{impact}</li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ClimateRisksTab;
