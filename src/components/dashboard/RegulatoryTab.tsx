
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { useRegulationsData } from './regulatory/useRegulationsData';
import RegulationsGrid from './regulatory/RegulationsGrid';
import RegulationsLoading from './regulatory/RegulationsLoading';
import RegulationsError from './regulatory/RegulationsError';
import { RegulatoryTabProps } from './regulatory/types';

const RegulatoryTab: React.FC<RegulatoryTabProps> = ({
  selectedRegion,
  setSelectedRegion,
  regions,
  isLoading: isLoadingProp,
  error: errorProp,
  selectedSector
}) => {
  const {
    regulationsData,
    loading,
    error,
    initialLoad,
    handleRefresh
  } = useRegulationsData(selectedSector);

  // Use fresh data if available
  const regulatoryDataToDisplay = regulationsData.length > 0 ? regulationsData : [];
  
  // Determine loading state - use prop if provided, otherwise use local state
  const isLoading = isLoadingProp !== undefined ? isLoadingProp : loading;
  
  // Determine if we should show the skeleton loading state
  const showSkeleton = initialLoad || isLoading;
  
  // Determine error state - use prop if provided, otherwise use local state
  const displayError = errorProp || error;

  // Filter regulations based on selected region
  const filteredRegulations = regulatoryDataToDisplay.filter(
    reg => selectedRegion === 'All' || reg.region === selectedRegion
  );

  // Display loading state
  if (showSkeleton) {
    return <RegulationsLoading regions={regions} selectedRegion={selectedRegion} />;
  }

  // Display error state
  if (displayError) {
    return <RegulationsError error={displayError} onRefresh={handleRefresh} />;
  }

  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words">Regulations Landscape</h2>
      
      <p className="text-muted-foreground mb-6">
        Key climate regulations and policies that impact your sector.
      </p>
      
      {/* Region filter pills */}
      <div className="flex flex-wrap gap-2 mb-4">
        {regions.map((region) => (
          <Button
            key={region}
            variant={selectedRegion === region ? "default" : "outline"}
            className={selectedRegion === region ? "bg-recrea-turquoise text-white" : ""}
            onClick={() => setSelectedRegion(region)}
          >
            {region}
          </Button>
        ))}
      </div>
      
      <RegulationsGrid regulations={filteredRegulations} />
    </div>
  );
};

export default RegulatoryTab;
