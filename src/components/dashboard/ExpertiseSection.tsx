import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { Card } from '@/components/ui/card';
import expertsData from '@/data/expertiseData';

interface Region {
  code: string;
  name: string;
}

interface Expert {
  name: string;
  about: string;
  highestEducation: string;
  linkedIn?: string;
  experience: number;
  region: Region;
  languages: string[];
  expertise: string[];
  charge: number;
  rating: number;
  projects: number;
}

interface ExpertiseSectionProps {
  experts: Expert[];
}

const ExpertiseSection: React.FC<ExpertiseSectionProps> = ({ experts }) => {
  return (
    <section className="rounded-xl">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {experts.map((expert, idx) => (
          <div key={idx} className="rounded-xl border p-6 flex flex-col gap-3 shadow-sm">
            <div className="flex items-center gap-4 mb-2">
              <div className="w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 text-2xl font-bold">
                <User className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <div className="font-semibold text-lg">{expert.name}</div>
                <div className="text-muted-foreground text-sm">{expert.highestEducation}</div>
              </div>
              {expert.linkedIn && (
                <a href={expert.linkedIn} target="_blank" rel="noopener noreferrer" className="ml-auto" style={{ color: '#0A66C2' }}>
                  <Linkedin className="h-5 w-5" />
                </a>
              )}
            </div>
            <div className="text-muted-foreground text-sm mb-1 line-clamp-2" style={{display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical', overflow: 'hidden'}}>
              {expert.about}
            </div>
            <div className="flex items-center gap-2 text-green-700 font-semibold text-base mb-1">
              <Star className="h-4 w-4 fill-green-600 text-green-600" />
              {expert.rating} <span className="text-muted-foreground font-normal text-sm">({expert.projects} projects)</span>
            </div>
            <div className="text-sm mb-1"><span className="font-semibold">Experience:</span> {expert.experience} years</div>
            <div className="text-sm mb-1"><span className="font-semibold">Region:</span> {expert.region?.name}</div>
            <div className="text-sm mb-1"><span className="font-semibold">Languages:</span> {expert.languages.join(', ')}</div>
            <div className="text-sm mb-2"><span className="font-semibold">Expertise:</span> {expert.expertise.map((ex, i) => (
              <span key={i} className="inline-block bg-recrea-light-mint text-green-700 rounded-full px-3 py-0.5 text-xs font-medium mr-2 mb-1 border border-green-200">{ex}</span>
            ))}</div>
            <div className="text-sm font-semibold text-green-700 mt-auto">Consultation: ${expert.charge}/hour</div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default ExpertiseSection;
