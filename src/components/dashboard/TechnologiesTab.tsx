import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Technology, fetchTechnologies } from '@/utils/searchApi';
import { AlertCircle, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { apiCache, generateCacheKey } from '@/utils/apiCache';
import FetchingIndicator from './shared/FetchingIndicator';

interface TechnologiesTabProps {
  selectedSector: string;
  sectorOptions: Array<{ value: string; label: string; }>;
  technologiesData?: Technology[];
  isLoading?: boolean;
  error?: string | null;
}

const TechnologiesTab: React.FC<TechnologiesTabProps> = ({
  selectedSector,
  sectorOptions,
  technologiesData: cachedData,
  isLoading: isLoadingProp,
  error: errorProp
}) => {
  const navigate = useNavigate();
  const [technologiesData, setTechnologiesData] = useState<Technology[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [initialLoad, setInitialLoad] = useState(true);
  const { toast, dismiss, toasts } = useToast();
  const lastLoadedSectorRef = React.useRef<string>("");

  // Get the correctly formatted sector name for the API
  const getFormattedSectorName = (): string => {
    const selectedOption = sectorOptions.find(s => s.value === selectedSector);
    return selectedOption?.label || "";
  };

  const formatSector = getFormattedSectorName();
  const cacheKey = generateCacheKey('technologies', { sector: formatSector });

  // Function to load technologies with caching
  const loadTechnologies = async (sectorName: string, forceRefresh = false) => {
    if (!sectorName) {
      console.warn("TechnologiesTab: No valid sector name provided");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      let techData: Technology[] | null;
      
      // Use cache if available and not forcing refresh
      if (!forceRefresh && apiCache.has(cacheKey)) {
        techData = apiCache.get<Technology[]>(cacheKey);
        console.log(`TechnologiesTab: Using cached data for ${sectorName}`, techData);
      } else {
        console.log(`TechnologiesTab: Fetching technologies for sector: ${sectorName}`);
        techData = await fetchTechnologies(sectorName, { toast, dismiss, toasts });
        
        // Cache the successful response
        if (techData && techData.length > 0) {
          apiCache.set(cacheKey, techData);
        }
      }
      
      if (techData && techData.length > 0) {
        setTechnologiesData(techData);
        lastLoadedSectorRef.current = sectorName;
      } else {
        console.warn("TechnologiesTab: Empty or invalid technologies data received");
        setTechnologiesData([]);
        
        // Only set error if we got no data at all
        if (!techData) {
          setError("No technologies data available for this sector");
        }
      }
    } catch (err: any) {
      console.error("TechnologiesTab: Error loading technologies:", err);
      setError(err.message || "Failed to load technologies data");
      toast({
        title: 'Error fetching technologies',
        description: err.message || 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setInitialLoad(false);
      console.log("TechnologiesTab: Finished loading technologies");
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    const sectorName = getFormattedSectorName();
    setRetryCount(prev => prev + 1);
    apiCache.remove(cacheKey); // Remove from cache to force refresh
    loadTechnologies(sectorName, true);
  };

  // Always fetch when sector changes or on retry
  useEffect(() => {
    const sectorName = getFormattedSectorName();
    if (sectorName) {
      loadTechnologies(sectorName);
    }
    
    return () => {
      // Cleanup - nothing specific needed here
    };
  }, [selectedSector, retryCount, sectorOptions]);

  // Use fresh data if available, otherwise fall back to cached data
  const displayData = technologiesData.length > 0 ? technologiesData : [];
  const isLoading = isLoadingProp !== undefined ? isLoadingProp : loading;
  const displayError = errorProp || error;
  const showLoadingIndicator = initialLoad || isLoading;

  // Helper function to determine badge styling based on maturity
  const getMaturityBadgeStyle = (maturity?: string) => {
    if (!maturity) return "bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-white";
    
    switch(maturity.toLowerCase()) {
      case "commercial":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
      case "early commercial":
      case "pilot":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100";
      case "r&d":
      case "conceptual":
        return "bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-white";
      default:
        return "bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-white";
    }
  };

  // Helper function to determine badge variant based on impact
  const getImpactBadgeVariant = (impact?: string) => {
    if (!impact) return "outline";
    
    switch(impact.toLowerCase()) {
      case "transformative":
        return "default";
      case "high":
        return "outline";
      case "medium":
      case "low":
        return "secondary";
      default:
        return "outline";
    }
  };

  // Loading state with spinner
  if (showLoadingIndicator) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Decarbonization Technologies for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <p className="text-muted-foreground mb-6">
          Emerging technologies that can help reduce emissions in your industry.
        </p>
        
        <FetchingIndicator message="Fetching Technologies..." />
      </div>
    );
  }

  // Error state
  if (displayError) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Decarbonization Technologies for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12 text-destructive">
          <AlertCircle className="w-12 h-12 mb-4" />
          <p className="text-lg font-medium">{displayError}</p>
          <p className="text-muted-foreground mt-2 mb-4">Please try again later or contact support if the issue persists.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  // Empty state
  if (displayData.length === 0) {
    return (
      <div className="glass-card p-4 md:p-8">
        <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
          Decarbonization Technologies for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
        </h2>
        
        <div className="flex flex-col items-center justify-center p-12">
          <FileText className="w-12 h-12 mb-4 text-muted-foreground" />
          <p className="text-lg font-medium">No Technologies Found</p>
          <p className="text-muted-foreground mt-2 mb-4">We couldn't find any decarbonization technologies for this sector. Please try another sector or contact support.</p>
          <Button onClick={handleRefresh} variant="outline">Try Again</Button>
        </div>
      </div>
    );
  }

  const handleTechnologyClick = (tech: Technology) => {
    const techId = tech.id?.toString() || tech.title.toLowerCase().replace(/\s+/g, '-');
    navigate(`/dashboard/technologies/${techId}`, {
      state: { topic: { ...tech, id: techId } }
    });
  };

  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words flex-wrap min-w-0">
        Decarbonization Technologies for {sectorOptions.find(s => s.value === selectedSector)?.label || 'Selected Sector'}
      </h2>
      
      <p className="text-muted-foreground mb-6">
        Emerging technologies that can help reduce emissions in your industry.
      </p>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {displayData.map(tech => (
          <Card 
            key={tech.id} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleTechnologyClick(tech)}
          >
            <CardHeader>
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{tech.title}</CardTitle>
                <div className="space-x-1">
                  <Badge className={getMaturityBadgeStyle(tech.maturity)}>
                    {tech.maturity}
                  </Badge>
                  <Badge 
                    variant={getImpactBadgeVariant(tech.impact)} 
                    className={tech.impact?.toLowerCase() === "transformative" ? "bg-recrea-turquoise" : ""}
                  >
                    {tech.impact} Impact
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                {tech.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TechnologiesTab;
