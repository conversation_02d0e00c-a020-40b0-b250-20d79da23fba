
import React from 'react';
import { Loader2 } from "lucide-react";

interface FetchingIndicatorProps {
  message: string;
}

const FetchingIndicator: React.FC<FetchingIndicatorProps> = ({ message }) => {
  return (
    <div className="flex justify-center items-center h-64 flex-col gap-2">
      <Loader2 className="animate-spin w-6 h-6 text-muted-foreground" />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
};

export default FetchingIndicator;
