
export interface Regulation {
  id?: number;
  title: string;
  region: string;
  status: string;
  description: string;
  date?: string;
  period?: string;
  value?: string;
}

export interface RegulatoryTabProps {
  regulatoryData: Regulation[];
  selectedRegion: string;
  setSelectedRegion: (region: string) => void;
  regions: string[];
  isLoading: boolean;
  error: string | null;
  selectedSector: string;
}
