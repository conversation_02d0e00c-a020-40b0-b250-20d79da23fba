
import React from 'react';
import { Button } from "@/components/ui/button";
import FetchingIndicator from '../shared/FetchingIndicator';

interface RegulationsLoadingProps {
  regions: string[];
  selectedRegion: string;
}

const RegulationsLoading: React.FC<RegulationsLoadingProps> = ({ regions, selectedRegion }) => {
  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words">Regulations Landscape</h2>
      
      <p className="text-muted-foreground mb-6">
        Key climate regulations and policies that impact your sector.
      </p>
      
      {/* Region filter pills */}
      <div className="flex flex-wrap gap-2 mb-6 opacity-50 pointer-events-none">
        {regions.map((region) => (
          <Button
            key={region}
            variant={selectedRegion === region ? "default" : "outline"}
            className={selectedRegion === region ? "bg-recrea-turquoise text-white" : ""}
            disabled
          >
            {region}
          </Button>
        ))}
      </div>
      
      <FetchingIndicator message="Fetching Regulatory Landscape..." />
    </div>
  );
};

export default RegulationsLoading;
