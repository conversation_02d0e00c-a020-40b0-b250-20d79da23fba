
import { useState, useEffect, useRef } from 'react';
import { useToast } from "@/hooks/use-toast";
import { callSearchApi } from '@/utils/searchApi';
import { apiCache, generateCacheKey } from '@/utils/apiCache';
import { Regulation } from './types';

export const useRegulationsData = (selectedSector: string) => {
  const [regulationsData, setRegulationsData] = useState<Regulation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [initialLoad, setInitialLoad] = useState(true);
  const { toast, dismiss, toasts } = useToast();
  const lastFetchedSectorRef = useRef<string>("");

  // Handle sector name formatting for API
  const getFormattedSectorName = (): string => {
    if (!selectedSector) return "";
    return selectedSector.charAt(0).toUpperCase() + selectedSector.slice(1).replace(/-/g, ' ');
  };

  const formatSector = getFormattedSectorName();
  const cacheKey = generateCacheKey('regulations', { sector: formatSector });

  // Function to load regulations with caching
  const loadRegulations = async (sectorName: string, forceRefresh = false) => {
    if (!sectorName) {
      console.warn("RegulatoryTab: No sector selected, skipping API fetch");
      setLoading(false);
      setInitialLoad(false);
      return;
    }

    console.log(`RegulatoryTab: Preparing to fetch regulations for sector: ${sectorName}`);
    setLoading(true);
    setError(null);
    
    try {
      let extractedRegulations: Regulation[] = [];
      
      // Use cache if available and not forcing refresh
      if (!forceRefresh && apiCache.has(cacheKey)) {
        extractedRegulations = apiCache.get<Regulation[]>(cacheKey) || [];
        console.log(`RegulatoryTab: Using cached data for ${sectorName}`, extractedRegulations);
      } else {
        console.log(`RegulatoryTab: Fetching regulations for sector: ${sectorName}`);
        
        // Make API call
        const accessToken = localStorage.getItem('accessToken');
        
        if (!accessToken) {
          throw new Error('No access token found. Please log in again.');
        }
        
        const response = await callSearchApi({
          query: `What are the key regulations relevant to ${sectorName} industry?`,
          search_type: "local",
          technology_sector: sectorName,
          question_type: "Regulatory"
        }, { toast, dismiss, toasts });
        
        if (response) {
          console.log("RegulatoryTab: API response received:", response);
          
          // Extract regulations data from response
          try {
            if (typeof response.response === 'string') {
              const parsedResponse = JSON.parse(response.response);
              extractedRegulations = parsedResponse.regulations || [];
            } else if (typeof response.response === 'object') {
              const responseObj = response.response as any;
              extractedRegulations = responseObj.regulations || [];
            }
            
            // Add IDs to regulations if they don't have them
            extractedRegulations = extractedRegulations.map((reg, index) => ({
              ...reg,
              id: reg.id || index + 1
            }));
            
            console.log("RegulatoryTab: Parsed regulations:", extractedRegulations);
            
            // Cache the successful response
            if (extractedRegulations.length > 0) {
              apiCache.set(cacheKey, extractedRegulations);
            }
          } catch (parseError) {
            console.error("RegulatoryTab: Error parsing regulations:", parseError);
            throw new Error("Failed to parse regulations data");
          }
        } else {
          throw new Error("Failed to fetch regulations data");
        }
      }
      
      // Update state with fetched data
      if (extractedRegulations.length > 0) {
        setRegulationsData(extractedRegulations);
        lastFetchedSectorRef.current = sectorName;
      } else {
        console.warn("RegulatoryTab: Empty regulations array returned from API");
        setRegulationsData([]);
      }
    } catch (err: any) {
      console.error("RegulatoryTab: Error loading regulations:", err);
      setError(err.message || "Failed to load regulations data");
      setRegulationsData([]);
      toast({
        title: 'Error fetching regulations',
        description: err.message || 'Please try again later',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setInitialLoad(false);
      console.log("RegulatoryTab: Finished loading regulations");
    }
  };

  // Handle manual refresh
  const handleRefresh = () => {
    const sectorName = getFormattedSectorName();
    setRetryCount(prev => prev + 1);
    apiCache.remove(cacheKey);
    loadRegulations(sectorName, true);
  };

  // Add effect to ensure initialLoad is set to false after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialLoad(false);
    }, 300);
    
    return () => clearTimeout(timer);
  }, []);

  // Fetch regulations only when selectedSector changes or on retry
  useEffect(() => {
    const sectorName = getFormattedSectorName();
    if (sectorName && (lastFetchedSectorRef.current !== sectorName || retryCount > 0)) {
      loadRegulations(sectorName);
    }
  }, [selectedSector, retryCount]);

  return {
    regulationsData,
    loading,
    error,
    initialLoad,
    handleRefresh
  };
};
