
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

interface RegulationsErrorProps {
  error: string;
  onRefresh: () => void;
}

const RegulationsError: React.FC<RegulationsErrorProps> = ({ error, onRefresh }) => {
  return (
    <div className="glass-card p-4 md:p-8">
      <h2 className="text-xl md:text-2xl font-semibold break-words">Regulations Landscape</h2>
      
      <div className="flex flex-col items-center justify-center p-12 text-destructive">
        <AlertCircle className="w-12 h-12 mb-4" />
        <p className="text-lg font-medium">Failed to load regulations</p>
        <p className="text-muted-foreground mt-2 mb-4">{error}</p>
        <Button onClick={onRefresh} variant="outline">Try Again</Button>
      </div>
    </div>
  );
};

export default RegulationsError;
