
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Regulation } from './types';

interface RegulationsGridProps {
  regulations: Regulation[];
}

const RegulationsGrid: React.FC<RegulationsGridProps> = ({ regulations }) => {
  const navigate = useNavigate();

  const handleRegulationClick = (regulation: Regulation) => {
    const regulationId = regulation.id?.toString() || regulation.title.toLowerCase().replace(/\s+/g, '-');
    navigate(`/dashboard/regulatory/${regulationId}`, {
      state: { topic: regulation }
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'enacted':
        return 'default';
      case 'pending':
      case 'planning':
        return 'secondary';
      case 'transition period':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (regulations.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground">No regulations found for the selected region.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {regulations.map((regulation, index) => {
        const regulationId = regulation.id?.toString() || `regulation-${index}`;
        return (
          <Card 
            key={regulationId} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => handleRegulationClick(regulation)}
          >
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg leading-tight">{regulation.title}</CardTitle>
                <Badge variant={getStatusBadgeVariant(regulation.status)} className="ml-2">
                  {regulation.status}
                </Badge>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>{regulation.region}</span>
                <span>•</span>
                <span>{regulation.date}</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground line-clamp-3">
                {regulation.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default RegulationsGrid;
