
import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useSectors } from '@/hooks/useSectors';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Plus, Search, Loader2 } from 'lucide-react';
import { useToastContext } from '@/contexts/ToastContext';
import {
  fetchActivities,
  createActivity,
  associateActivityWithSector,
  generateNumberedActivityName,
  ActivityResponse,
  ActivityCreate
} from '@/services/activitiesApi';

// Helper function to get sector UUID from industry ID using actual sectors data
const getSectorUuidFromIndustryId = (industryId: string, sectors: any[]): string => {
  if (!sectors || sectors.length === 0) {
    console.warn('No sectors available');
    return 'default-sector-uuid';
  }

  // Normalize industry ID for comparison
  const normalizedIndustryId = industryId.toLowerCase().replace(/[-_]/g, ' ');

  // Try to find a matching sector based on industry ID
  const matchingSector = sectors.find((sector: any) => {
    const normalizedSectorName = sector.name.toLowerCase().replace(/[-_]/g, ' ');
    return normalizedSectorName.includes(normalizedIndustryId) ||
           normalizedIndustryId.includes(normalizedSectorName);
  });

  if (matchingSector) {
    console.log(`Found matching sector: ${matchingSector.name} (${matchingSector.uuid}) for industry: ${industryId}`);
    return matchingSector.uuid;
  }

  // If no match found, return the first sector (fallback)
  console.warn(`No matching sector found for industry: ${industryId}, using first available sector: ${sectors[0].name}`);
  return sectors[0].uuid;
};

interface ActivitySelectorProps {
  industryId: string;
  onActivitySelect: (activityName: string) => void;
  onCancel: () => void;
  placeholder?: string;
  label?: string;
  selectedActivity?: string;
  existingNodeNames?: string[];
}

export const ActivitySelector: React.FC<ActivitySelectorProps> = ({
  industryId,
  onActivitySelect,
  onCancel,
  placeholder = "Select an activity...",
  label = "Activity",
  selectedActivity = "",
  existingNodeNames = []
}) => {
  const toastContext = useToastContext();
  const { sectors, isLoading: sectorsLoading } = useSectors();
  const [activities, setActivities] = useState<ActivityResponse[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<ActivityResponse[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newActivityName, setNewActivityName] = useState('');
  const [newActivityDescription, setNewActivityDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [currentSectorUuid, setCurrentSectorUuid] = useState<string | null>(null);

  // Define loadActivities function first
  const loadActivities = useCallback(async () => {
    if (!currentSectorUuid) {
      console.warn('No sector UUID available, skipping activity load');
      return;
    }

    console.log('Starting to load activities for sector:', currentSectorUuid);
    setIsLoading(true);
    try {
      const fetchedActivities = await fetchActivities(currentSectorUuid, toastContext);
      console.log('Fetched activities:', fetchedActivities);
      setActivities(fetchedActivities);
      setFilteredActivities(fetchedActivities);
    } catch (error) {
      console.error('Error loading activities:', error);
    } finally {
      setIsLoading(false);
    }
  }, [currentSectorUuid, toastContext]);

  // Get sector UUID when sectors are loaded
  useEffect(() => {
    console.log('Sectors loaded:', sectors.length, 'Industry ID:', industryId);
    if (sectors.length > 0 && industryId) {
      const sectorUuid = getSectorUuidFromIndustryId(industryId, sectors);
      console.log('Setting sector UUID:', sectorUuid);
      setCurrentSectorUuid(sectorUuid);
    }
  }, [sectors, industryId]);

  // Load activities when sector UUID is available
  useEffect(() => {
    if (currentSectorUuid) {
      console.log('Loading activities for sector UUID:', currentSectorUuid);
      loadActivities();
    }
  }, [currentSectorUuid, loadActivities]);

  // Filter activities based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredActivities(activities);
    } else {
      const filtered = activities.filter(activity =>
        activity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (activity.description && activity.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredActivities(filtered);
    }
  }, [activities, searchTerm]);

  const handleActivitySelect = (activityName: string) => {
    console.log('Activity selected:', activityName);
    console.log('Existing node names:', existingNodeNames);

    if (activityName === 'create-new') {
      setShowCreateDialog(true);
      return;
    }

    // Generate numbered name if duplicate exists in existing nodes
    const numberedName = generateNumberedActivityName(activityName, existingNodeNames);
    console.log('Generated numbered name:', numberedName);
    onActivitySelect(numberedName);
  };

  const handleCreateActivity = async () => {
    if (!newActivityName.trim()) {
      toastContext.toast({
        title: "Validation Error",
        description: "Activity name is required",
        variant: "destructive"
      });
      return;
    }

    if (!currentSectorUuid) {
      toastContext.toast({
        title: "Error",
        description: "No sector information available",
        variant: "destructive"
      });
      return;
    }

    setIsCreating(true);
    try {
      const activityData: ActivityCreate = {
        name: newActivityName.trim(),
        description: newActivityDescription.trim() || undefined,
        sector_uuid: currentSectorUuid
      };

      const newActivity = await createActivity(activityData, toastContext);

      if (newActivity) {
        // Associate the activity with the sector
        await associateActivityWithSector(currentSectorUuid, newActivity.uuid, toastContext);

        // Add the new activity to the list
        const updatedActivities = [...activities, newActivity];
        setActivities(updatedActivities);
        setFilteredActivities(updatedActivities);

        // Generate numbered name and select it
        const numberedName = generateNumberedActivityName(newActivity.name, existingNodeNames);
        onActivitySelect(numberedName);

        // Close dialog and reset form
        setShowCreateDialog(false);
        setNewActivityName('');
        setNewActivityDescription('');
      }
    } catch (error) {
      console.error('Error creating activity:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCancelCreate = () => {
    setShowCreateDialog(false);
    setNewActivityName('');
    setNewActivityDescription('');
  };

  return (
    <>
      <div className="space-y-2">
        <Label htmlFor="activity-selector">{label}</Label>
        
        {/* Search input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            id="activity-search"
            placeholder="Search activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Activity selector */}
        <Select value={selectedActivity} onValueChange={handleActivitySelect}>
          <SelectTrigger>
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
          <SelectContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading activities...
              </div>
            ) : (
              <>
                {/* Add New Activity option */}
                <SelectItem value="create-new" className="font-medium text-blue-600">
                  <div className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add New Activity
                  </div>
                </SelectItem>
                
                {/* Separator */}
                {filteredActivities.length > 0 && (
                  <div className="border-t my-1" />
                )}
                
                {/* Activity options */}
                {filteredActivities.map((activity) => (
                  <SelectItem key={activity.uuid} value={activity.name}>
                    <div className="flex flex-col">
                      <span>{activity.name}</span>
                      {activity.description && (
                        <span className="text-xs text-gray-500 truncate">
                          {activity.description}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
                
                {/* No results message */}
                {!isLoading && filteredActivities.length === 0 && searchTerm && (
                  <div className="py-4 text-center text-gray-500 text-sm">
                    No activities found for "{searchTerm}"
                  </div>
                )}
              </>
            )}
          </SelectContent>
        </Select>

        {/* Action buttons */}
        <div className="flex gap-2 pt-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>

      {/* Create New Activity Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Activity</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="new-activity-name">Activity Name *</Label>
              <Input
                id="new-activity-name"
                value={newActivityName}
                onChange={(e) => setNewActivityName(e.target.value)}
                placeholder="Enter activity name"
                autoFocus
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-activity-description">Description (Optional)</Label>
              <Input
                id="new-activity-description"
                value={newActivityDescription}
                onChange={(e) => setNewActivityDescription(e.target.value)}
                placeholder="Enter activity description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelCreate}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateActivity}
              disabled={isCreating || !newActivityName.trim()}
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                'Create Activity'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
