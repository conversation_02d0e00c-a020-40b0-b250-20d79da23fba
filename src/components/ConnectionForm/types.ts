
// Define shared types for connection form components
export interface OutputForm {
  id: string;
  targetNode: string;
  outputTechnology: string;
  energyOutputs: EnergyOutput[]; // Changed from single energyOutput to array
  matOutputs: MaterialOutput[]; // Changed from single matOutput to array
}

// New interface for energy output
export interface EnergyOutput {
  id: string; // Unique identifier for each energy output
  energy: string;
  unit: string;
  sec: string;
  final: boolean;
  connect: string;
  qty: string;
  qtyUnit: string;
  destinationTechnology?: string; // New field for technology of the destination activity
}

// New interface for material output
export interface MaterialOutput {
  id: string; // Unique identifier for each material output
  material: string;
  unit: string;
  smc: string;
  final: boolean;
  connect: string; // This property was missing in the interface
  qty: string;
  qtyUnit: string;
  destinationTechnology?: string; // New field for technology of the destination activity
}

// Simplified interface for emission by-products
export interface EmissionByProduct {
  id: string;
  name: string;
  factor: string;
  unit: string;
}

// Interface for energy by-product entries
export interface EnergyByProduct {
  byproduct: string;
  unit: string;
  bppo: string;
  connect: string;
  replaced: string;
  emissions?: EmissionByProduct[]; // Added emissions array property
  technology?: string; // Optional technology field for By-products tab
}

// Interface for material by-product entries
export interface MaterialByProduct {
  byproduct: string;
  unit: string;
  bppo: string;
  connect: string;
  replaced: string;
  techEmissionFactor: string;
  emissionFactor: string;
  emissionUnit: string;
  technology?: string; // Optional technology field for By-products tab
}

// New interface for emission entries
export interface EmissionEntry {
  id: string; // Unique identifier for each emission
  name: string;
  factor: string;
  unit: string;
}

// New interfaces for dynamic input entries
export interface EnergyInput {
  id: string;
  source: string;
  unit: string;
  cost: string;
  sec: string;
  sourceActivity?: string; // New field for source activity
  technology?: string;     // New field for technology
}

export interface EmissionInput {
  id: string;
  source: string;
  factor: string; // Changed from ef to factor for clarity
  unit: string;
}

export interface MaterialInput {
  id: string;
  material: string;
  unit: string;
  cost: string;
  smc: string;
  sourceActivity?: string; // New field for source activity
  technology?: string;     // New field for technology
}

// Financial data structure for a technology-activity pair
export interface FinancialData {
  capacity: string;
  capacityUnit: string;
  capitalCostUnit: string;
  omCost: string;
}

// Record of financial entries keyed by technology-activity pair
export interface FinancialEntries {
  [key: string]: FinancialData;
}

export interface FormData {
  // Basic info
  activity: string;
  technology: string;
  // Technology life span
  startYear?: string;
  endYear?: string;
  // Manual entry fields - kept for compatibility but not used in UI anymore
  customTechnology: string;
  customActivity: string;
  
  // Inputs section - now arrays for multiple entries
  energyInputs: EnergyInput[];
  emissions: EmissionInput[];
  materialInputs: MaterialInput[];
  
  // Legacy single entries - kept for compatibility but not used in UI
  energyInput: {
    source: string;
    unit: string;
    cost: string;
    sec: string;
  };
  emission: {
    source: string;
    ef: string;
    unit: string;
  };
  matInput: {
    material: string;
    unit: string;
    cost: string;
    smc: string;
  };
  
  // By-products section
  byproductTechnology: string; // Shared technology field for all by-products
  byproductEnergy: {
    byproduct: string;
    unit: string;
    bppo: string;
    connect: string;
    replaced: string;
  };
  byproductMat: {
    byproduct: string;
    unit: string;
    bppo: string;
    connect: string;
    replaced: string;
    techEmissionFactor: string;
    emissionFactor: string;
    emissionUnit: string;
  };
  
  // Multiple by-products support
  energyByProducts?: EnergyByProduct[];
  materialByProducts?: MaterialByProduct[];
  
  // Financial section - original single entry
  financial: FinancialData;
  
  // NEW: Multiple financial entries for different technology-activity pairs
  financialEntries?: FinancialEntries;
}

export interface ConnectionFormProps {
  open: boolean;
  onClose: () => void;
  onComplete: (formData: FormData) => void;
  autoFillInputs?: AutoFillInput[];
  sourceNode?: SourceNode;
  targetNode?: TargetNode;
  availableNodes?: AvailableNode[];
  incomingConnectionData?: IncomingConnectionData;
  existingFormData?: {
    formData?: FormData;
    outputs?: OutputForm[];
    technologies?: string[];
    technologyFormData?: Record<string, FormData>;
  };
  // New props for API integration
  sectorUuid?: string;
  activityUuid?: string;
  industryId?: string;
}

// Define proper types for props - Updated AutoFillInput interface
export interface AutoFillInput {
  id: string;
  data: {
    label?: string;
  };
  technology?: string;
  nodeName?: string;
  outputs?: {
    energy?: string;
    energyUnit?: string;
    energySEC?: string;
    material?: string;
    materialUnit?: string;
    materialSMC?: string;
  };
}

export interface SourceNode {
  id: string;
  data: {
    label?: string;
  };
}

export interface TargetNode {
  id: string;
  data: {
    label?: string;
  };
}

export interface AvailableNode {
  id: string;
  data: {
    label?: string;
  };
}

export interface IncomingConnectionData {
  [key: string]: any;
}

export type StepId = "outputs" | "byproducts" | "inputs" | "financial";

export interface Step {
  id: StepId;
  label: string;
}

// Define dummy data for selects - keep these as they are for fallback
export const energySources = ["Energy 1", "Energy 2"];
export const emissions = ["CO2", "NOx", "SOx", "PM10", "CH4"];
export const units = ["GJ", "Tonnes"];
export const materials = [
  "Material 1", "Material 2", "Material 3", // Keep existing for compatibility
  "Crude oil", "Raw gas", "Sour gas", "Sweet gas", "Saturated gas",
  "Dry gas", "Purified gas", "Processed gas", "Natural gas"
];
export const byproducts = ["By-product 1", "By-product 2"];
export const techs = ["Boiler", "Turbine"];
export const activities = ["Steam generation", "Cracking"];

// API Response interfaces
export interface TechnologyResponse {
  uuid: string;
  name: string;
  description?: string;
}

export interface MaterialResponse {
  uuid: string;
  name: string;
  unit?: string;
}

export interface EnergyResponse {
  uuid: string;
  name: string;
  unit?: string;
}

export interface EmissionResponse {
  uuid: string;
  name: string;
  unit?: string;
}

// Define steps centrally
export const STEPS: Step[] = [
  { id: "inputs", label: "Inputs" },
  { id: "outputs", label: "Outputs" },
  { id: "byproducts", label: "By-products" },
  { id: "financial", label: "Financial" }
];
