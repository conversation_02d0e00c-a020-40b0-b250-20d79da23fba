
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface EnergyByProduct {
  id: string;
  name: string;
  unit: string;
  bppo: string;
}

interface MaterialByProduct {
  id: string;
  name: string;
  unit: string;
  bppo: string;
}

interface ByProductsTabProps {
  selectedTechnology: string;
  energyByProducts: EnergyByProduct[];
  materialByProducts: MaterialByProduct[];
  onNext: () => void;
  onBack: () => void;
  onUpdateEnergyByProducts: (byProducts: EnergyByProduct[]) => void;
  onUpdateMaterialByProducts: (byProducts: MaterialByProduct[]) => void;
}

const energyNames = ['Steam', 'Hot Water', 'Compressed Air', 'Electricity'];
const materialNames = ['Natural gas', 'Hydrogen', 'Oxygen', 'Nitrogen', 'Water'];
const units = ['Tonnes', 'kg', 'L', 'm³', 'GJ', 'MJ'];

export const ByProductsTab: React.FC<ByProductsTabProps> = ({
  selectedTechnology,
  energyByProducts,
  materialByProducts,
  onNext,
  onBack,
  onUpdateEnergyByProducts,
  onUpdateMaterialByProducts
}) => {
  const updateEnergyByProduct = (id: string, field: keyof EnergyByProduct, value: string) => {
    const updatedByProducts = energyByProducts.map(byProduct => 
      byProduct.id === id ? { ...byProduct, [field]: value } : byProduct
    );
    onUpdateEnergyByProducts(updatedByProducts);
  };

  const updateMaterialByProduct = (id: string, field: keyof MaterialByProduct, value: string) => {
    const updatedByProducts = materialByProducts.map(byProduct => 
      byProduct.id === id ? { ...byProduct, [field]: value } : byProduct
    );
    onUpdateMaterialByProducts(updatedByProducts);
  };

  return (
    <div className="space-y-6">
      {/* Technology Badge */}
      <div className="text-center">
        <span className="inline-block bg-gray-100 text-gray-800 px-4 py-2 rounded-full text-sm font-medium">
          {selectedTechnology}
        </span>
      </div>

      {/* Energy By-product Section */}
      <div className="border-2 border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Energy By-product</h3>
        
        {energyByProducts.length === 0 ? (
          <p className="text-gray-500 text-sm">No energy by-products configured</p>
        ) : (
          <div className="space-y-6">
            {energyByProducts.map((byProduct, index) => (
              <div key={byProduct.id} className="bg-white rounded-lg p-4 border border-green-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-700">By-product {index + 1}</h4>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <Select 
                      value={byProduct.name} 
                      onValueChange={(value) => updateEnergyByProduct(byProduct.id, 'name', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select energy" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {energyNames.map(name => (
                          <SelectItem key={name} value={name}>{name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                    <Select 
                      value={byProduct.unit} 
                      onValueChange={(value) => updateEnergyByProduct(byProduct.id, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">BP/PO</label>
                    <Input
                      type="number"
                      value={byProduct.bppo}
                      onChange={(e) => updateEnergyByProduct(byProduct.id, 'bppo', e.target.value)}
                      placeholder="Enter BP/PO value"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Material By-product Section */}
      <div className="border-2 border-purple-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Material By-product</h3>
        
        {materialByProducts.length === 0 ? (
          <p className="text-gray-500 text-sm">No material by-products configured</p>
        ) : (
          <div className="space-y-6">
            {materialByProducts.map((byProduct, index) => (
              <div key={byProduct.id} className="bg-white rounded-lg p-4 border border-purple-200">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-700">By-product {index + 1}</h4>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <Select 
                      value={byProduct.name} 
                      onValueChange={(value) => updateMaterialByProduct(byProduct.id, 'name', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select material" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {materialNames.map(name => (
                          <SelectItem key={name} value={name}>{name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                    <Select 
                      value={byProduct.unit} 
                      onValueChange={(value) => updateMaterialByProduct(byProduct.id, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">BP/PO</label>
                    <Input
                      type="number"
                      value={byProduct.bppo}
                      onChange={(e) => updateMaterialByProduct(byProduct.id, 'bppo', e.target.value)}
                      placeholder="Enter BP/PO value"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext} className="bg-green-500 hover:bg-green-600 text-white">
          Next
        </Button>
      </div>
    </div>
  );
};
