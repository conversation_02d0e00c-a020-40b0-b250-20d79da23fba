
import React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className }) => {
  return (
    <Link to="/" className={`flex items-center ${className}`}>
      <h1 className="text-2xl md:text-3xl font-bold">
        <span className="text-recrea-green">recre8</span>
        <span className="dark:text-white text-recrea-dark">.earth</span>
      </h1>
    </Link>
  );
};

export default Logo;
