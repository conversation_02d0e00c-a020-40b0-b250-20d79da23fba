import React, { useState } from 'react';
import {
  Dialog,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { ActivitySelector } from './ActivitySelector';

interface NodeCreationDialogProps {
  open: boolean;
  onClose: () => void;
  onNodeCreate: (activityName: string) => void;
  industryId: string;
  title?: string;
  description?: string;
  existingNodeNames?: string[];
}

export const NodeCreationDialog: React.FC<NodeCreationDialogProps> = ({
  open,
  onClose,
  onNodeCreate,
  industryId,
  title = "Create New Node",
  description = "Select an activity for the new node. You can choose from existing activities or create a new one.",
  existingNodeNames = []
}) => {
  const [selectedActivity, setSelectedActivity] = useState('');

  const handleActivitySelect = (activityName: string) => {
    console.log('NodeCreationDialog: handleActivitySelect called with:', activityName);
    onNodeCreate(activityName);
    setSelectedActivity('');
    onClose();
  };

  const handleCancel = () => {
    setSelectedActivity('');
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <ActivitySelector
            industryId={industryId}
            onActivitySelect={handleActivitySelect}
            onCancel={handleCancel}
            placeholder="Select an activity for this node..."
            label="Node Activity"
            selectedActivity={selectedActivity}
            existingNodeNames={existingNodeNames}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
