
import React, { useState, useRef, useEffect } from 'react';
import { Search as SearchIcon, History, Bookmark, BookmarkCheck, ArrowRight } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Command, CommandInput, CommandList, CommandEmpty, CommandGroup, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/hooks/use-toast';

// Mock data for search suggestions
const suggestionData = [
  "What are the key drivers for decarbonization in steel?",
  "How does carbon pricing affect manufacturing?",
  "What is green hydrogen and its role in steel production?",
  "Best practices for energy transition in heavy industry",
  "Net zero strategies for manufacturing companies",
  "Latest carbon capture technologies in steel",
  "Regulatory frameworks for emissions in EU vs US",
];

// Mock data for search results
const resultsData = [
  {
    id: 1,
    title: "Green Hydrogen in Steel Manufacturing",
    category: "Decarbonization",
    summary: "Green hydrogen is emerging as a key technology for decarbonizing steel production, replacing coal in the reduction process.",
    content: "Green hydrogen is produced through electrolysis powered by renewable energy, making it a zero-carbon alternative to coal in steel production. When used in direct reduction of iron ore, it can significantly reduce the carbon footprint of steel manufacturing. Several pilot projects across Europe show promising results with up to 95% carbon emission reductions compared to traditional blast furnace methods."
  },
  {
    id: 2,
    title: "Carbon Price Mechanisms for Heavy Industry",
    category: "Carbon Price",
    summary: "Understanding different carbon pricing models and their impact on heavy industrial sectors like steel, cement and chemicals.",
    content: "Carbon pricing mechanisms vary from carbon taxes to emissions trading systems (ETS). For heavy industry, the EU ETS is the most established system, with prices ranging from €25-€90 per tonne of CO2. These mechanisms create financial incentives for decarbonization but also pose competitiveness challenges, especially in trade-exposed sectors. Border carbon adjustments are being developed to address carbon leakage concerns."
  },
  {
    id: 3,
    title: "Net Zero Roadmap for Steel Industry",
    category: "Net Zero",
    summary: "Strategic pathways for steel manufacturers to achieve net zero emissions by 2050, including technology adoption and energy efficiency.",
    content: "A comprehensive net zero roadmap for steel includes multiple parallel strategies: (1) Maximizing scrap-based production via electric arc furnaces, (2) Hydrogen-based direct reduction for primary steel, (3) Carbon capture for remaining blast furnaces, (4) Energy efficiency improvements across all processes, and (5) Procurement of renewable electricity. The roadmap should include clear milestones for 2030, 2040, and 2050 with defined emissions reduction targets."
  },
  {
    id: 4,
    title: "Energy Transition Case Studies in Manufacturing",
    category: "Energy Transition",
    summary: "Real-world examples of successful energy transition initiatives in global manufacturing companies.",
    content: "Leading manufacturing companies have demonstrated successful energy transitions through various approaches. SSAB in Sweden has implemented hydrogen-based steelmaking. ThyssenKrupp is investing in direct reduction with hydrogen. Voestalpine has developed a hybrid blast furnace approach. These case studies show that a combination of technological innovation, strategic investments, and policy support is essential for successful energy transition in manufacturing."
  },
];

interface SearchHistoryItem {
  query: string;
  timestamp: number;
}

interface SavedQuery {
  id: number;
  query: string;
}

const Search: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchResults, setSearchResults] = useState<typeof resultsData>([]);
  const [showHistory, setShowHistory] = useState(false);
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [savedQueries, setSavedQueries] = useState<SavedQuery[]>([]);
  const [hasSearched, setHasSearched] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Categories for filter buttons
  const categories = ['All', 'Net Zero', 'Carbon Price', 'Energy Transition', 'Decarbonization', 'Other Objectives'];

  // Filter suggestions as user types
  useEffect(() => {
    if (searchQuery.length > 1) {
      const filtered = suggestionData.filter(item => 
        item.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  // Handle search submission
  const handleSearch = () => {
    if (!searchQuery.trim()) {
      toast({
        title: "Empty Search",
        description: "Please enter a search query",
        variant: "destructive",
      });
      return;
    }

    // Filter results based on category and query
    let results = [...resultsData];
    
    if (selectedCategory !== 'All') {
      results = results.filter(item => item.category === selectedCategory);
    }
    
    results = results.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.summary.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setSearchResults(results);
    setHasSearched(true);
    
    // Add to search history
    const newHistoryItem: SearchHistoryItem = {
      query: searchQuery,
      timestamp: Date.now()
    };
    
    setSearchHistory(prev => [newHistoryItem, ...prev.slice(0, 9)]);
    setShowSuggestions(false);
  };

  // Handle pressing Enter in search input
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Select a suggestion
  const selectSuggestion = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    setTimeout(() => handleSearch(), 100);
  };

  // Handle selecting from history
  const selectFromHistory = (historyItem: SearchHistoryItem) => {
    setSearchQuery(historyItem.query);
    setShowHistory(false);
    setTimeout(() => handleSearch(), 100);
  };

  // Save/unsave a query
  const toggleSaveQuery = (query: string) => {
    const existing = savedQueries.find(item => item.query === query);
    
    if (existing) {
      setSavedQueries(savedQueries.filter(item => item.query !== query));
      toast({
        title: "Query Removed",
        description: "Query has been removed from saved list",
      });
    } else {
      const newSaved: SavedQuery = {
        id: Date.now(),
        query: query
      };
      setSavedQueries([...savedQueries, newSaved]);
      toast({
        title: "Query Saved",
        description: "Query has been added to your saved list",
      });
    }
  };

  // Check if a query is saved
  const isQuerySaved = (query: string) => {
    return savedQueries.some(item => item.query === query);
  };

  // Format timestamp
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // View full response
  const viewFullResponse = (resultId: number) => {
    const result = resultsData.find(item => item.id === resultId);
    
    if (result) {
      toast({
        title: result.title,
        description: result.content,
        duration: 10000,
      });
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-8">
      <div className="glass-card p-8">
        <h2 className="text-2xl font-semibold mb-6">Search Knowledge Base</h2>
        
        {/* Search Bar */}
        <div className="relative mb-6">
          <div className="flex">
            <div className="relative flex-1">
              <Input
                ref={inputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter Your Queries Here..."
                className="glass-input pr-10 py-6 text-lg"
              />
              <div className="absolute right-3 top-1/2 -translate-y-1/2">
                <Button 
                  onClick={handleSearch} 
                  variant="ghost" 
                  size="icon"
                  className="text-muted-foreground hover:text-primary"
                >
                  <SearchIcon size={20} />
                </Button>
              </div>
            </div>
            
            {/* History Button */}
            <Popover open={showHistory} onOpenChange={setShowHistory}>
              <PopoverTrigger asChild>
                <Button 
                  variant="outline" 
                  className="ml-2 py-6"
                  disabled={searchHistory.length === 0}
                >
                  <History size={20} />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-0" align="end">
                <Command>
                  <CommandInput placeholder="Search history..." />
                  <CommandList>
                    <CommandEmpty>No search history found.</CommandEmpty>
                    <CommandGroup heading="Recent Searches">
                      {searchHistory.map((item, index) => (
                        <CommandItem 
                          key={index} 
                          onSelect={() => selectFromHistory(item)}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center">
                            <History size={16} className="mr-2 opacity-70" />
                            <span>{item.query}</span>
                          </div>
                          <span className="text-xs opacity-50">{formatTime(item.timestamp)}</span>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                    {savedQueries.length > 0 && (
                      <CommandGroup heading="Saved Queries">
                        {savedQueries.map((item) => (
                          <CommandItem 
                            key={item.id} 
                            onSelect={() => selectFromHistory({ query: item.query, timestamp: Date.now() })}
                          >
                            <BookmarkCheck size={16} className="mr-2 text-recrea-turquoise" />
                            <span>{item.query}</span>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    )}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          
          {/* Autocomplete Suggestions */}
          {showSuggestions && (
            <div className="absolute z-10 w-full mt-1 bg-card rounded-lg border border-border shadow-lg">
              <ul className="py-1">
                {filteredSuggestions.map((suggestion, index) => (
                  <li 
                    key={index}
                    className="px-4 py-2 hover:bg-accent cursor-pointer flex items-center justify-between"
                    onClick={() => selectSuggestion(suggestion)}
                  >
                    <div className="flex items-center">
                      <SearchIcon size={14} className="mr-2 opacity-60" />
                      <span>{suggestion}</span>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleSaveQuery(suggestion);
                      }}
                    >
                      {isQuerySaved(suggestion) ? (
                        <BookmarkCheck size={14} className="text-recrea-turquoise" />
                      ) : (
                        <Bookmark size={14} />
                      )}
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
        
        {/* Category Filters */}
        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className={selectedCategory === category ? "bg-recrea-turquoise text-white" : ""}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
        
        {/* Search Results */}
        {hasSearched && (
          <div className="mt-6">
            <h3 className="text-xl font-medium mb-4">
              Search Results {searchResults.length > 0 && `(${searchResults.length})`}
            </h3>
            
            {searchResults.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {searchResults.map((result) => (
                  <Card key={result.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{result.title}</CardTitle>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8 -mt-1 -mr-2"
                          onClick={() => toggleSaveQuery(result.title)}
                        >
                          {isQuerySaved(result.title) ? (
                            <BookmarkCheck size={16} className="text-recrea-turquoise" />
                          ) : (
                            <Bookmark size={16} />
                          )}
                        </Button>
                      </div>
                      <div className="inline-block px-2 py-1 text-xs rounded-full bg-recrea-light-mint text-recrea-dark dark:bg-recrea-card-bg dark:text-white">
                        {result.category}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm line-clamp-3">
                        {result.summary}
                      </CardDescription>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="ml-auto flex items-center gap-1 text-xs"
                        onClick={() => viewFullResponse(result.id)}
                      >
                        View More
                        <ArrowRight size={14} />
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center p-8">
                <div className="rounded-full bg-recrea-light-mint w-16 h-16 mx-auto flex items-center justify-center mb-4">
                  <SearchIcon size={24} className="text-recrea-turquoise" />
                </div>
                <h4 className="text-lg font-medium mb-2">No results found</h4>
                <p className="text-muted-foreground">Try a different query or adjust your filters</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Search;
