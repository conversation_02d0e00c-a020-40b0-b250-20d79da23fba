
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface RenameNodeDialogProps {
  open: boolean;
  onClose: () => void;
  onRename: (newName: string) => void;
  currentName?: string;
}

export const RenameNodeDialog: React.FC<RenameNodeDialogProps> = ({
  open,
  onClose,
  onRename,
  currentName = ''
}) => {
  const [newName, setNewName] = useState(currentName);

  const handleRename = () => {
    if (newName.trim()) {
      onRename(newName.trim());
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Rename Node</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="node-name">Node Name</Label>
            <Input
              id="node-name"
              value={newName}
              onChange={(e) => setNewName(e.target.value)}
              placeholder="Enter new node name"
              autoFocus
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleRename} disabled={!newName.trim()}>
            Rename
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
