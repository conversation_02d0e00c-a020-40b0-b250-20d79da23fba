
import React, { useState, useEffect } from 'react';
import { Note } from '@/types/Note';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Bold, Italic, Underline, AlignLeft, AlignCenter, AlignRight, List, ListOrdered, Image, Link, Save } from 'lucide-react';

interface NoteEditorProps {
  selectedNote: Note | null;
  onUpdateNote: (note: Note) => void;
  onSaveNote: (note: Note) => void;
}

const NoteEditor: React.FC<NoteEditorProps> = ({ 
  selectedNote, 
  onUpdateNote,
  onSaveNote
}) => {
  const { toast } = useToast();
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [titleError, setTitleError] = useState<string | null>(null);
  
  useEffect(() => {
    if (selectedNote) {
      setTitle(selectedNote.title);
      setContent(selectedNote.content);
      setTitleError(null); // Clear any previous errors when switching notes
    } else {
      setTitle('');
      setContent('');
      setTitleError(null);
    }
  }, [selectedNote]);
  
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
    setTitleError(null); // Clear error when user starts typing
  };
  
  const handleSave = () => {
    if (selectedNote) {
      // Validate title
      if (!title.trim()) {
        setTitleError("Title cannot be empty");
        toast({
          title: "Validation Error",
          description: "Please enter a title for your note",
          variant: "destructive"
        });
        return;
      }
      
      const updatedNote = {
        ...selectedNote,
        title,
        content,
        updated_at: new Date()
      };
      onSaveNote(updatedNote);
      toast({
        title: "Note saved",
        description: "Your note has been saved successfully",
      });
    }
  };

  if (!selectedNote) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center p-6 text-muted-foreground">
        <StickyNoteIcon size={48} className="mb-4 opacity-50" />
        <h3 className="text-xl font-semibold mb-2">No note selected</h3>
        <p className="text-sm text-center">Select a note from the sidebar or create a new one</p>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      <div className="p-4 border-b flex items-center justify-between">
        <div className="flex-1">
          <Input
            value={title}
            onChange={handleTitleChange}
            placeholder="Untitled"
            className={`text-lg font-medium border-none focus-visible:ring-0 ${titleError ? 'border-red-500 focus:border-red-500' : ''}`}
          />
          {titleError && (
            <p className="text-xs text-red-500 mt-1">{titleError}</p>
          )}
        </div>
        <Button 
          onClick={handleSave}
          className="bg-purple-600 hover:bg-purple-700"
        >
          <Save size={16} className="mr-2" /> Save Note
        </Button>
      </div>
      
      <div className="border-b p-2 flex gap-2 flex-wrap">
        <Button variant="ghost" size="icon">
          <Bold size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <Italic size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <Underline size={18} />
        </Button>
        <div className="w-px h-6 bg-border mx-1 self-center" />
        <Button variant="ghost" size="icon">
          <AlignLeft size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <AlignCenter size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <AlignRight size={18} />
        </Button>
        <div className="w-px h-6 bg-border mx-1 self-center" />
        <Button variant="ghost" size="icon">
          <List size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <ListOrdered size={18} />
        </Button>
        <div className="w-px h-6 bg-border mx-1 self-center" />
        <Button variant="ghost" size="icon">
          <Link size={18} />
        </Button>
        <Button variant="ghost" size="icon">
          <Image size={18} />
        </Button>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto">
        <Textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Start writing your note here..."
          className="min-h-full resize-none border-none focus-visible:ring-0 p-0"
        />
      </div>
      
      <div className="p-2 border-t text-xs text-muted-foreground flex items-center justify-end">
        {`Words: ${content.trim() ? content.trim().split(/\s+/).length : 0} · Characters: ${content.length}`}
      </div>
    </div>
  );
};

// SVG icon for empty state
const StickyNoteIcon = (props: any) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M15.5 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2V8.5L15.5 3Z" />
    <path d="M15 3v6h6" />
  </svg>
);

export default NoteEditor;
