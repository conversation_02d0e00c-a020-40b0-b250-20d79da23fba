import React from 'react';
import { Note } from '@/types/Note';
import { StickyNote, Plus, Edit, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface NotesListProps {
  notes: Note[];
  selectedNoteId: string | null;
  onSelectNote: (id: string) => void;
  onCreateNewNote: () => void;
  onDeleteNote: (id: string) => void;
}

const NotesList: React.FC<NotesListProps> = ({ 
  notes, 
  selectedNoteId, 
  onSelectNote, 
  onCreateNewNote,
  onDeleteNote 
}) => {
  return (
    <div className="w-64 border-r h-full flex flex-col bg-card/50 dark:bg-background/20">
      <div className="p-4 border-b bg-muted/30 dark:bg-background/30">
        <h3 className="font-bold text-xl mb-3">All Notes</h3>
        <Button 
          onClick={onCreateNewNote}
          className="w-full bg-recrea-turquoise hover:bg-recrea-teal text-white"
        >
          <Plus size={16} className="mr-2" /> Create new note
        </Button>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {notes.length > 0 ? (
          notes.map((note) => (
            <div 
              key={note.uuid}
              onClick={() => onSelectNote(note.uuid)}
              className={`p-3 cursor-pointer flex items-center justify-between hover:bg-accent/50 group
                ${selectedNoteId === note.uuid ? 'bg-accent/70 dark:bg-background/40 border-l-2 border-primary' : ''}`}
            >
              <div className="flex items-center gap-2">
                <div className={`p-1 rounded ${selectedNoteId === note.uuid ? 'bg-primary/10' : ''}`}>
                  <StickyNote size={16} className={`${selectedNoteId === note.uuid ? 'text-primary' : 'text-muted-foreground'}`} />
                </div>
                <span className="truncate text-sm">{note.title || 'Untitled'}</span>
              </div>
              <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6 text-muted-foreground hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteNote(note.uuid);
                  }}
                >
                  <Trash2 size={14} />
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
            <StickyNote size={24} className="mb-2 opacity-50" />
            <p className="text-sm text-center">No notes found</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default NotesList;
