
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';

interface WelcomePageProps {
  onCreateNewNote: () => void;
}

const WelcomePage: React.FC<WelcomePageProps> = ({ onCreateNewNote }) => {
  return (
    <div className="flex-1 flex flex-col items-center justify-center p-6">
      <div className="max-w-lg text-center">
        <h1 className="text-2xl font-bold mb-4">Welcome to Notes Creator</h1>
        <p className="text-muted-foreground mb-8">
          Your platform for professional notations. Craft, organize, and capture insights with precision.
        </p>
        <Button 
          onClick={onCreateNewNote}
          className="bg-recrea-turquoise hover:bg-recrea-teal text-white px-6"
        >
          Create a new note
        </Button>
      </div>
    </div>
  );
};

export default WelcomePage;
