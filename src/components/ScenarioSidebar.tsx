import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { FileText, Upload, X, ChevronRight, ChevronLeft, StickyNote } from 'lucide-react';
import { useToastContext } from '@/contexts/ToastContext';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  uploadedAt: Date;
}
interface ScenarioSidebarProps {
  scenarioId?: string;
  isVisible?: boolean;
  onToggle?: () => void;
}
export const ScenarioSidebar: React.FC<ScenarioSidebarProps> = ({
  scenarioId,
  isVisible = true,
  onToggle
}) => {
  const {
    toast
  } = useToastContext();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [notes, setNotes] = useState('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const acceptedFileTypes = '.pdf,.docx,.xlsx,.csv,.png,.jpg,.jpeg';
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  const handleNotesChange = (value: string) => {
    setNotes(value);
  };
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    Array.from(files).forEach(file => {
      // Validate file size
      if (file.size > maxFileSize) {
        toast({
          title: 'File too large',
          description: `${file.name} exceeds 10MB limit`,
          variant: 'destructive'
        });
        return;
      }

      // Validate file type
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!acceptedFileTypes.includes(fileExtension)) {
        toast({
          title: 'Invalid file type',
          description: `${file.name} is not a supported format`,
          variant: 'destructive'
        });
        return;
      }
      const newFile: UploadedFile = {
        id: `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadedAt: new Date()
      };
      setUploadedFiles(prev => [...prev, newFile]);
    });

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  const handleRemoveFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };
  const handleSaveNotes = async () => {
    setIsSaving(true);
    try {
      // Save notes and files metadata to localStorage for now
      const scenarioData = {
        notes,
        files: uploadedFiles,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem(`scenario-metadata-${scenarioId}`, JSON.stringify(scenarioData));
      toast({
        title: 'Notes saved',
        description: 'Your change notes have been saved successfully'
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Could not save notes. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };
  const formatTimestamp = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  if (!isVisible) {
    return <div className="fixed right-0 top-1/2 -translate-y-1/2 z-30">
        <Button variant="outline" size="sm" onClick={onToggle} className="rounded-l-md rounded-r-none border-r-0 bg-white shadow-md flex flex-col items-center gap-1 h-16 w-12" title="Open scenario notes and documents">
          <StickyNote size={16} />
          <span className="text-xs writing-mode-vertical transform rotate-180" style={{
          writingMode: 'vertical-rl'
        }}>
            Notes
          </span>
        </Button>
      </div>;
  }
  return <div className="fixed right-0 top-0 h-full w-80 bg-white border-l border-gray-200 shadow-lg z-20 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Notes for scenario</h3>
        {onToggle && <Button variant="ghost" size="sm" onClick={onToggle}>
            <ChevronRight size={16} />
          </Button>}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Notes Section */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText size={16} />
              Notes
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea placeholder="Describe the changes you made for this scenario..." value={notes} onChange={e => handleNotesChange(e.target.value)} className="min-h-[100px] resize-none" />
            <Button onClick={handleSaveNotes} disabled={isSaving || !notes.trim()} size="sm" className="w-full">
              {isSaving ? 'Saving...' : 'Save Notes'}
            </Button>
          </CardContent>
        </Card>

        {/* File Upload Section */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Upload size={16} />
              Supporting Documents
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <input ref={fileInputRef} type="file" accept={acceptedFileTypes} multiple onChange={handleFileUpload} className="hidden" />
              <Button variant="outline" onClick={() => fileInputRef.current?.click()} className="w-full">
                <Upload size={16} className="mr-2" />
                Upload Files
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                PDF, DOCX, XLSX, CSV, PNG, JPG up to 10MB
              </p>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && <div className="space-y-2">
                <Label className="text-xs font-medium text-gray-700">
                  Uploaded Files ({uploadedFiles.length})
                </Label>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {uploadedFiles.map(file => <div key={file.id} className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(file.size)} • {formatTimestamp(file.uploadedAt)}
                        </p>
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => handleRemoveFile(file.id)} className="ml-2 p-1 h-auto">
                        <X size={14} />
                      </Button>
                    </div>)}
                </div>
              </div>}
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          Changes are automatically saved with your scenario
        </p>
      </div>
    </div>;
};
