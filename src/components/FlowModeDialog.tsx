
import React from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { LayoutGrid, Cog, FolderPlus, Link2, Cpu, ShoppingBag } from "lucide-react";

export type FlowModeType = "base" | "scratch" | "subflow" | "byproduct" | "technology" | "marketplace";

export interface FlowModeDialogProps {
  open: boolean;
  nodeLabel?: string; // If present, show subflow/byproduct actions
  onClose?: () => void;
  onSelect: (mode: FlowModeType) => void;
}

export function FlowModeDialog({
  open,
  nodeLabel,
  onSelect,
  onClose
}: FlowModeDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md p-6">
        <DialogTitle className="sr-only">Flow Mode Selection</DialogTitle>
        <div className="flex flex-col gap-7 items-center">
          <div className="w-full flex flex-col gap-2 mb-2">
            <div className="text-xl font-bold text-center">How would you like to start assessing risks?</div>
            <div className="text-sm text-center text-gray-500">
              Choose a method to create your flow diagram. You can use a standard template or build from scratch.
            </div>
          </div>
          {!nodeLabel && (
          <div className="w-full flex flex-col gap-5">
            <Button
              variant="outline"
              onClick={() => onSelect("base")}
              className="flex items-center justify-start px-7 py-5 gap-4 rounded-xl border-2 border-[#ded3fd] bg-[#EFEAFB] text-[#7E69AB] shadow-sm hover:bg-[#f5f2fc] text-lg font-semibold"
            >
              <LayoutGrid size={32} />
              <span className="flex flex-col text-left">
                <span>Use standard template</span>
                <span className="text-xs font-normal text-gray-500">Load a pre-built template</span>
              </span>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSelect("scratch")}
              className="flex items-center justify-start px-7 py-5 gap-4 rounded-xl border-2 border-[#ded3fd] bg-white shadow-sm hover:bg-[#f5f2fc] text-lg font-semibold"
            >
              <Cog size={32} />
              <span className="flex flex-col text-left">
                <span>Build from scratch</span>
              </span>
            </Button>
          </div>
          )}
          {nodeLabel && (
          <div className="w-full flex flex-col gap-5">
            <Button
              variant="outline"
              onClick={() => onSelect("subflow")}
              className="flex items-center justify-start px-5 py-4 gap-4 rounded-xl border-2 border-[#d3e4fd] bg-[#eaf4ff] text-[#4062a7] shadow-sm text-lg font-semibold hover:bg-[#d3e4fd]"
              data-testid="create-subflow"
            >
              <FolderPlus size={32} />
              <span className="flex flex-col text-left">
                <span>Create Subflow</span>
                <span className="text-xs font-normal text-gray-500">
                  Start a branch from <span className="font-semibold">'{nodeLabel}'</span>
                </span>
              </span>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSelect("byproduct")}
              className="flex items-center justify-start px-5 py-4 gap-4 rounded-xl border-2 border-[#ffdee2] bg-[#fff1f3] text-[#b46d8f] shadow-sm text-lg font-semibold hover:bg-[#ffeeef]"
              data-testid="create-byproduct"
            >
              <Link2 size={32} />
              <span className="flex flex-col text-left">
                <span>Create Byproduct Flow</span>
                <span className="text-xs font-normal text-gray-500">
                  Start a byproduct branch from <span className="font-semibold">'{nodeLabel}'</span>
                </span>
              </span>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSelect("technology")}
              className="flex items-center justify-start px-5 py-4 gap-4 rounded-xl border-2 border-[#d3fdde] bg-[#eafff4] text-[#40a762] shadow-sm text-lg font-semibold hover:bg-[#d3fde4]"
              data-testid="create-technology"
            >
              <Cpu size={32} />
              <span className="flex flex-col text-left">
                <span>Technology</span>
                <span className="text-xs font-normal text-gray-500">
                  Add technology for <span className="font-semibold">'{nodeLabel}'</span>
                </span>
              </span>
            </Button>
            <Button
              variant="outline"
              onClick={() => onSelect("marketplace")}
              className="flex items-center justify-start px-5 py-4 gap-4 rounded-xl border-2 border-[#fdefd3] bg-[#fff8ea] text-[#a77640] shadow-sm text-lg font-semibold hover:bg-[#fde4d3]"
              data-testid="create-marketplace"
            >
              <ShoppingBag size={32} />
              <span className="flex flex-col text-left">
                <span>Marketplace</span>
                <span className="text-xs font-normal text-gray-500">
                  Find solutions for <span className="font-semibold">'{nodeLabel}'</span>
                </span>
              </span>
            </Button>
          </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
