
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface FinancialData {
  capacity: string;
  capacityUnit: string;
  capitalCostPerUnit: string;
  annualOMCost: string;
}

interface FinancialTabProps {
  selectedTechnology: string;
  financialData: FinancialData;
  onNext: () => void;
  onBack: () => void;
  onUpdateFinancialData: (data: FinancialData) => void;
}

const capacityUnits = [
  'Tonnes/day',
  'kg/day', 
  'Tonnes/hr',
  'kg/hr',
  'L/day',
  'L/hr',
  'm³/day',
  'm³/hr'
];

export const FinancialTab: React.FC<FinancialTabProps> = ({
  selectedTechnology,
  financialData,
  onNext,
  onBack,
  onUpdateFinancialData
}) => {
  const updateFinancialData = (field: keyof FinancialData, value: string) => {
    const updatedData = { ...financialData, [field]: value };
    onUpdateFinancialData(updatedData);
  };

  return (
    <div className="space-y-6">
      {/* Technology Badge */}
      <div className="text-center">
        <span className="inline-block bg-gray-100 text-gray-800 px-4 py-2 rounded-full text-sm font-medium">
          {selectedTechnology}
        </span>
      </div>

      {/* Financial Information Section */}
      <div className="space-y-6">
        <h2 className="text-lg font-bold text-gray-800">Financial Information</h2>
        
        <div className="border-2 border-green-200 rounded-lg p-6 bg-green-50/30">
          {/* Technology Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-base font-medium text-gray-700">
              Technology: {selectedTechnology}
            </h3>
            <ChevronUp className="w-4 h-4 text-gray-500" />
          </div>

          {/* Financial Input Fields */}
          <div className="grid grid-cols-3 gap-6">
            {/* Capacity */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Capacity
              </label>
              <Input
                type="number"
                value={financialData.capacity}
                onChange={(e) => updateFinancialData('capacity', e.target.value)}
                placeholder="1"
                className="w-full"
              />
            </div>

            {/* Capacity Unit */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Capacity Unit
              </label>
              <Select 
                value={financialData.capacityUnit} 
                onValueChange={(value) => updateFinancialData('capacityUnit', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Tonnes/day" />
                </SelectTrigger>
                <SelectContent className="bg-white border shadow-lg z-50">
                  {capacityUnits.map(unit => (
                    <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Capital Cost / Unit Capacity */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Capital Cost / Unit Capacity
              </label>
              <Input
                type="number"
                value={financialData.capitalCostPerUnit}
                onChange={(e) => updateFinancialData('capitalCostPerUnit', e.target.value)}
                placeholder="1"
                className="w-full"
              />
            </div>
          </div>

          {/* Annual O&M Cost - Full Width */}
          <div className="mt-6 max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Annual O&M Cost
            </label>
            <Input
              type="number"
              value={financialData.annualOMCost}
              onChange={(e) => updateFinancialData('annualOMCost', e.target.value)}
              placeholder="1"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t">
        <Button 
          variant="outline" 
          onClick={onBack}
          disabled={true}
          className="opacity-50 cursor-not-allowed"
        >
          Back
        </Button>
        <Button 
          onClick={onNext} 
          className="bg-green-500 hover:bg-green-600 text-white px-8"
        >
          Next
        </Button>
      </div>
    </div>
  );
};
