import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { supplierTechData } from '@/data/supplierTechData';

const energyTypes = Object.keys(supplierTechData);

// TODO: Define these interfaces in a shared types file
interface EnergyInput {
  id: string;
  name: string;
  unit: string;
  costPerUnit: string;
  sec: string;
  technology: string;
}

interface Emission {
  id: string;
  name: string;
  emissionFactor: string;
  unit: string;
}

interface MaterialInput {
  id: string;
  name: string;
  unit: string;
  costPerUnit: string;
}

interface InputsTabProps {
  selectedTechnology: string;
  onSelectTechnology: (value: string) => void;
  technologies: string[];
  
  energyInputs: EnergyInput[];
  updateEnergyInput: (id: string, field: keyof EnergyInput, value: string) => void;
  setSelectedEnergyInput: (value: string) => void;
  energyUnits: string[];

  emissions: Emission[];
  updateEmission: (id: string, field: keyof Emission, value: string) => void;
  emissionTypes: string[];
  emissionUnits: string[];

  materialInputs: MaterialInput[];
  updateMaterialInput: (id: string, field: keyof MaterialInput, value: string) => void;
  isMaterialInputOpen: boolean;
  onToggleMaterialInput: (open: boolean) => void;
}

export const InputsTab: React.FC<InputsTabProps> = ({
  selectedTechnology,
  onSelectTechnology,
  technologies,
  energyInputs,
  updateEnergyInput,
  setSelectedEnergyInput,
  energyUnits,
  emissions,
  updateEmission,
  emissionTypes,
  emissionUnits,
  materialInputs,
  updateMaterialInput,
  isMaterialInputOpen,
  onToggleMaterialInput,
}) => {
  return (
    <div className="space-y-6">
      {/* Technology Selection */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Technology</label>
          <Select value={selectedTechnology} onValueChange={onSelectTechnology}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {technologies.map(tech => (
                <SelectItem key={tech} value={tech}>{tech}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Energy Input Section */}
      <div className="border-2 border-blue-200 rounded-lg p-4">
        <h4 className="font-medium mb-4">Energy Input</h4>

        {energyInputs.map((energyInput) => (
          <div key={energyInput.id} className="space-y-4 mb-6">
            <div className="grid grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <Select 
                  value={energyInput.name} 
                  onValueChange={(value) => setSelectedEnergyInput(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {energyTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Unit</label>
                <Select 
                  value={energyInput.unit} 
                  onValueChange={(value) => updateEnergyInput(energyInput.id, 'unit', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {energyUnits.map(unit => (
                      <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Cost/Unit</label>
                <Input 
                  value={energyInput.costPerUnit}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'costPerUnit', e.target.value)}
                  readOnly
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">SEC</label>
                <Input 
                  value={energyInput.sec}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'sec', e.target.value)}
                  readOnly
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Select Technology</label>
              <Select 
                value={energyInput.technology} 
                onValueChange={(value) => updateEnergyInput(energyInput.id, 'technology', value)}
                disabled
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Nil">Nil</SelectItem>
                  {technologies.map(tech => (
                    <SelectItem key={tech} value={tech}>{tech}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
      </div>

      {/* Emissions Section */}
      <div className="border-2 border-blue-200 rounded-lg p-4">
        <h4 className="font-medium mb-4">Emissions</h4>

        {emissions.map((emission) => (
          <div key={emission.id} className="grid grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">Name</label>
              <Select 
                value={emission.name} 
                onValueChange={(value) => updateEmission(emission.id, 'name', value)}
                disabled
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  {emissionTypes.map(type => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Emission Factor</label>
              <Input 
                value={emission.emissionFactor}
                onChange={(e) => updateEmission(emission.id, 'emissionFactor', e.target.value)}
                readOnly
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Unit</label>
              <Select 
                value={emission.unit} 
                onValueChange={(value) => updateEmission(emission.id, 'unit', value)}
                disabled
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {emissionUnits.map(unit => (
                    <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        ))}
      </div>

      {/* Material Input Section */}
      <div className="border-2 border-gray-200 rounded-lg">
        <Collapsible open={isMaterialInputOpen} onOpenChange={onToggleMaterialInput}>
          <CollapsibleTrigger className="w-full p-4 flex justify-between items-center hover:bg-gray-50">
            <div className="flex items-center gap-2">
              <h4 className="font-medium">Material Input</h4>
            </div>
            {isMaterialInputOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
          </CollapsibleTrigger>
          <CollapsibleContent className="p-4 pt-0">
            {materialInputs.map((materialInput) => (
              <div key={materialInput.id} className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Name</label>
                    <Input 
                      value={materialInput.name}
                      onChange={(e) => updateMaterialInput(materialInput.id, 'name', e.target.value)}
                      placeholder="Material name"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Unit</label>
                    <Input 
                      value={materialInput.unit}
                      onChange={(e) => updateMaterialInput(materialInput.id, 'unit', e.target.value)}
                      placeholder="Unit"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Cost/Unit</label>
                    <Input 
                      value={materialInput.costPerUnit}
                      onChange={(e) => updateMaterialInput(materialInput.id, 'costPerUnit', e.target.value)}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}; 