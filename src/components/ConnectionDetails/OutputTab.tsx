
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface OutputEntry {
  id: string;
  name: string;
  unit: string;
  smc: string;
  isFinalOutput: boolean;
  finalOutputQuantity: string;
  finalOutputUnit: string;
}

interface OutputTabProps {
  selectedTechnology: string;
  energyOutputs: OutputEntry[];
  materialOutputs: OutputEntry[];
  onNext: () => void;
  onBack: () => void;
  onUpdateEnergyOutputs: (outputs: OutputEntry[]) => void;
  onUpdateMaterialOutputs: (outputs: OutputEntry[]) => void;
}

const materialNames = ['Natural gas', 'Hydrogen', 'Oxygen', 'Nitrogen', 'Water'];
const units = ['Tonnes', 'kg', 'L', 'm³', 'GJ', 'MJ'];

export const OutputTab: React.FC<OutputTabProps> = ({
  selectedTechnology,
  energyOutputs,
  materialOutputs,
  onNext,
  onBack,
  onUpdateEnergyOutputs,
  onUpdateMaterialOutputs
}) => {
  const updateMaterialOutput = (id: string, field: keyof OutputEntry, value: any) => {
    const updatedOutputs = materialOutputs.map(output => 
      output.id === id ? { ...output, [field]: value } : output
    );
    onUpdateMaterialOutputs(updatedOutputs);
  };

  return (
    <div className="space-y-6">
      {/* Technology Badge */}
      <div className="text-center">
        <span className="inline-block bg-gray-100 text-gray-800 px-4 py-2 rounded-full text-sm font-medium">
          {selectedTechnology}
        </span>
      </div>

      {/* Primary Energy Output Section */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Primary energy Output</h3>
        {energyOutputs.length === 0 ? (
          <p className="text-gray-500 text-sm">No energy outputs configured</p>
        ) : (
          <div className="space-y-4">
            {/* Energy outputs would be displayed here if they exist */}
          </div>
        )}
      </div>

      {/* Primary Material Output Section */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-700 mb-4">Primary material Output</h3>

        {materialOutputs.length === 0 ? (
          <p className="text-gray-500 text-sm">No material outputs configured</p>
        ) : (
          <div className="space-y-6">
            {materialOutputs.map((output) => (
              <div key={output.id} className="bg-white rounded-lg p-4 border">
                {/* Main row with Name, Unit, SMC */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <Select 
                      value={output.name} 
                      onValueChange={(value) => updateMaterialOutput(output.id, 'name', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select material" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {materialNames.map(name => (
                          <SelectItem key={name} value={name}>{name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                    <Select 
                      value={output.unit} 
                      onValueChange={(value) => updateMaterialOutput(output.id, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border shadow-lg z-50">
                        {units.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">SMC</label>
                    <Input
                      type="number"
                      value={output.smc}
                      onChange={(e) => updateMaterialOutput(output.id, 'smc', e.target.value)}
                      placeholder="Enter SMC value"
                      disabled={!output.name || !output.unit}
                      className="bg-gray-50"
                    />
                  </div>
                </div>

                {/* Final Output Checkbox */}
                {/* <div className="flex items-center space-x-2 mb-4">
                  <Checkbox
                    id={`final-output-${output.id}`}
                    checked={output.isFinalOutput}
                    onCheckedChange={(checked) => updateMaterialOutput(output.id, 'isFinalOutput', checked)}
                    className="border-orange-400 data-[state=checked]:bg-orange-500"
                  />
                  <label 
                    htmlFor={`final-output-${output.id}`}
                    className="text-sm font-medium text-orange-600"
                  >
                    Final output
                  </label>
                </div> */}

                {/* Final Output Quantity Fields (shown when checkbox is checked) */}
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Final output Quantity</label>
                      <Input
                        type="number"
                        value={output.finalOutputQuantity}
                        onChange={(e) => updateMaterialOutput(output.id, 'finalOutputQuantity', e.target.value)}
                        placeholder="Enter quantity"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Unit</label>
                      <Select 
                        value={output.finalOutputUnit || output.unit} 
                        onValueChange={(value) => updateMaterialOutput(output.id, 'finalOutputUnit', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-white border shadow-lg z-50">
                          {units.map(unit => (
                            <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between pt-6 border-t">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={onNext} className="bg-green-500 hover:bg-green-600 text-white">
          Next
        </Button>
      </div>
    </div>
  );
};
