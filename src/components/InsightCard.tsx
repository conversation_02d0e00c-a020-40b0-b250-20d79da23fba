
import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useTheme } from '@/components/ThemeProvider';

interface InsightCardProps {
  title: string;
  description: string;
  modalContent?: string;
  className?: string;
  style?: React.CSSProperties;
}

const InsightCard: React.FC<InsightCardProps> = ({ 
  title, 
  description, 
  modalContent,
  className = "",
  style
}) => {
  const { theme } = useTheme();
  
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div 
          className={`${theme === 'dark' 
            ? 'glass-card' 
            : 'bg-white shadow-sm border border-recrea-mint/50 rounded-xl'} 
            p-6 cursor-pointer hover:border-recrea-turquoise/50 group transition-all ${className}`}
          style={style}
        >
          <h3 className="text-xl font-semibold text-recrea-turquoise mb-3">{title}</h3>
          <p className={`${theme === 'dark' 
            ? 'dark:text-white dark:text-opacity-90' 
            : 'text-recrea-dark/90'} mb-4 line-clamp-3`}
          >
            {description}
          </p>
          <div className="text-sm text-recrea-turquoise hover:text-recrea-teal transition-colors group-hover:underline">
            Read more...
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className={`${theme === 'dark' 
        ? 'glass-card border-recrea-card-border' 
        : 'bg-white border border-recrea-mint shadow-xl'} sm:max-w-md`}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-recrea-turquoise mb-3">{title}</DialogTitle>
        </DialogHeader>
        <DialogDescription className={`${theme === 'dark' 
          ? 'dark:text-white dark:text-opacity-90' 
          : 'text-recrea-dark/90'}`}
        >
          {modalContent || description}
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
};

export default InsightCard;
