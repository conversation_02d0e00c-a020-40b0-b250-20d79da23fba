import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Plus, ChevronDown, ChevronUp } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { OutputTab } from './ConnectionDetails/OutputTab';
import { ByProductsTab } from './ConnectionDetails/ByProductsTab';
import { FinancialTab } from './ConnectionDetails/FinancialTab';
import { supplierTechData } from '@/data/supplierTechData';
import { InputsTab } from './ConnectionDetails/InputsTab';

interface EnergyInput {
  id: string;
  name: string;
  unit: string;
  costPerUnit: string;
  sec: string;
  technology: string;
}

interface Emission {
  id: string;
  name: string;
  emissionFactor: string;
  unit: string;
}

interface MaterialInput {
  id: string;
  name: string;
  unit: string;
  costPerUnit: string;
}

interface OutputEntry {
  id: string;
  name: string;
  unit: string;
  smc: string;
  isFinalOutput: boolean;
  finalOutputQuantity: string;
  finalOutputUnit: string;
}

interface EnergyByProduct {
  id: string;
  name: string;
  unit: string;
  bppo: string;
  energyReplaced: string;
}

interface MaterialByProduct {
  id: string;
  name: string;
  unit: string;
  bppo: string;
}

interface FinancialData {
  capacity: string;
  capacityUnit: string;
  capitalCostPerUnit: string;
  annualOMCost: string;
}

interface ConnectionDetailsModalProps {
  open: boolean;
  onClose: () => void;
  activityName?: string;
  initialTechnology?: string;
  energyInputs?: EnergyInput[];
  emissions?: Emission[];
  materialInputs?: MaterialInput[];
  onNext?: (data: any) => void;
  onBack?: () => void;
  onAddTechnology?: () => void;
}

const steps = [
  { id: 1, label: 'Financial', key: 'financial' },
  { id: 2, label: 'Inputs', key: 'inputs' },
  { id: 3, label: 'Outputs', key: 'outputs' },
  { id: 4, label: 'By-products', key: 'byproducts' }
];

const energyUnits = ['GJ', 'MJ', 'kWh', 'BTU'];
const emissionUnits = ['kg', 'tonnes', 'lb', 'g'];
const technologies = ['Boiler', 'Turbine', 'Heat Exchanger', 'Compressor'];
const energyTypes = Object.keys(supplierTechData);
const emissionTypes = ['CO2', 'NOx', 'SOx', 'PM10', 'CH4'];

export const ConnectionDetailsModal: React.FC<ConnectionDetailsModalProps> = ({
  open,
  onClose,
  activityName = 'Cryogenic Separation',
  initialTechnology = 'Boiler',
  energyInputs: initialEnergyInputs = [],
  emissions: initialEmissions = [],
  materialInputs: initialMaterialInputs = [],
  onNext,
  onBack,
  onAddTechnology
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedTechnology, setSelectedTechnology] = useState(initialTechnology);
  const [selectedEnergyInput, setSelectedEnergyInput] = useState(energyTypes[0]);
  
  const [energyInputs, setEnergyInputs] = useState<EnergyInput[]>([]);
  const [emissions, setEmissions] = useState<Emission[]>([]);
  const [materialInputs, setMaterialInputs] = useState<MaterialInput[]>([]);
  const [isMaterialInputOpen, setIsMaterialInputOpen] = useState(false);

  // New state for outputs
  const [energyOutputs, setEnergyOutputs] = useState<OutputEntry[]>([]);
  const [materialOutputs, setMaterialOutputs] = useState<OutputEntry[]>([]);

  // New state for by-products
  const [energyByProducts, setEnergyByProducts] = useState<EnergyByProduct[]>([]);

  const [materialByProducts, setMaterialByProducts] = useState<MaterialByProduct[]>([]);

  // New state for financial data
  const [financialData, setFinancialData] = useState<FinancialData>({
    capacity: '1',
    capacityUnit: 'Tonnes/day',
    capitalCostPerUnit: '1',
    annualOMCost: '1'
  });

  useEffect(() => {
    const data = supplierTechData[selectedEnergyInput];
    if (data) {
      setSelectedTechnology(data.technology);
      setEnergyInputs(data.energyInputs || []);
      setEmissions(data.emissions || []);
      setMaterialInputs(data.materialInputs || []);
      setIsMaterialInputOpen(true);
      setEnergyOutputs(data.energyOutputs || []);
      setMaterialOutputs(data.materialOutputs || []);
      setEnergyByProducts(data.energyByProducts || []);
      setMaterialByProducts(data.materialByProducts || []);
      setFinancialData(data.financialData || {
        capacity: '1',
        capacityUnit: 'Tonnes/day',
        capitalCostPerUnit: '1',
        annualOMCost: '1'
      });
    }
  }, [selectedEnergyInput]);

  const addMaterialInput = () => {
    const newMaterialInput: MaterialInput = {
      id: Date.now().toString(),
      name: '',
      unit: '',
      costPerUnit: ''
    };
    setMaterialInputs([...materialInputs, newMaterialInput]);
    setIsMaterialInputOpen(true);
  };

  const updateEnergyInput = (id: string, field: keyof EnergyInput, value: string) => {
    setEnergyInputs(energyInputs.map(input => 
      input.id === id ? { ...input, [field]: value } : input
    ));
  };

  const updateEmission = (id: string, field: keyof Emission, value: string) => {
    setEmissions(emissions.map(emission => 
      emission.id === id ? { ...emission, [field]: value } : emission
    ));
  };

  const updateMaterialInput = (id: string, field: keyof MaterialInput, value: string) => {
    setMaterialInputs(materialInputs.map(input => 
      input.id === id ? { ...input, [field]: value } : input
    ));
  };

  const handleNext = () => {
    const formData = {
      technology: selectedTechnology,
      energyInputs,
      emissions,
      materialInputs,
      energyOutputs,
      materialOutputs,
      energyByProducts,
      materialByProducts,
      financialData
    };
    onNext?.(formData);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1: // Financial
        return (
          <FinancialTab
            selectedTechnology={selectedTechnology}
            financialData={financialData}
            onNext={() => setCurrentStep(2)}
            onBack={() => onBack}
            onUpdateFinancialData={setFinancialData}
          />
        );

      case 2: // Inputs
        return (
          <InputsTab
            selectedTechnology={selectedTechnology}
            onSelectTechnology={setSelectedTechnology}
            technologies={technologies}
            energyInputs={energyInputs}
            updateEnergyInput={updateEnergyInput}
            setSelectedEnergyInput={setSelectedEnergyInput}
            energyUnits={energyUnits}
            emissions={emissions}
            updateEmission={updateEmission}
            emissionTypes={emissionTypes}
            emissionUnits={emissionUnits}
            materialInputs={materialInputs}
            updateMaterialInput={updateMaterialInput}
            isMaterialInputOpen={isMaterialInputOpen}
            onToggleMaterialInput={setIsMaterialInputOpen}
          />
        );

      case 3: // Outputs
        return (
          <OutputTab
            selectedTechnology={selectedTechnology}
            energyOutputs={energyOutputs}
            materialOutputs={materialOutputs}
            onNext={() => setCurrentStep(4)}
            onBack={() => setCurrentStep(2)}
            onUpdateEnergyOutputs={setEnergyOutputs}
            onUpdateMaterialOutputs={setMaterialOutputs}
          />
        );

      case 4: // By-products
        return (
          <ByProductsTab
            selectedTechnology={selectedTechnology}
            energyByProducts={energyByProducts}
            materialByProducts={materialByProducts}
            onNext={() => handleNext}
            onBack={() => setCurrentStep(3)}
            onUpdateEnergyByProducts={setEnergyByProducts}
            onUpdateMaterialByProducts={setMaterialByProducts}
          />
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="space-y-4">
          <DialogTitle className="text-xl font-semibold">
            {activityName}
          </DialogTitle>
          {/* <p className="text-sm text-gray-600">
            Capture output details and destination activities.
          </p> */}

          {/* Stepper */}
          <div className="flex items-center justify-center space-x-8 py-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div 
                  className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium cursor-pointer ${
                    currentStep === step.id 
                      ? 'bg-blue-500 text-white' 
                      : currentStep > step.id 
                        ? 'bg-green-500 text-white' 
                        : 'bg-gray-200 text-gray-600'
                  }`}
                  onClick={() => setCurrentStep(step.id)}
                >
                  {step.id}
                </div>
                <span 
                  className={`ml-2 text-sm cursor-pointer ${
                    currentStep === step.id ? 'text-blue-600 font-medium' : 'text-gray-600'
                  }`}
                  onClick={() => setCurrentStep(step.id)}
                >
                  {step.label}
                </span>
                {index < steps.length - 1 && (
                  <div className="w-8 h-px bg-gray-300 ml-4"></div>
                )}
              </div>
            ))}
          </div>
        </DialogHeader>

        {/* Step Content */}
        <div className="py-6">
          {renderStepContent()}
        </div>

        {/* Footer - Only show for Inputs step since other tabs have their own navigation */}
        {currentStep === 2 && (
          <div className="flex justify-between pt-6 border-t">
            <Button 
              variant="outline" 
              onClick={() => setCurrentStep(1)}
            >
              Back
            </Button>
            <Button 
              onClick={() => setCurrentStep(3)}
              className="bg-green-500 hover:bg-green-600"
            >
              Next
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
