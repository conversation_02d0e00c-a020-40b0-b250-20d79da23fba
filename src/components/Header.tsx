
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { HelpCircle, UserRound } from 'lucide-react';
import Logo from '@/components/Logo';
import ThemeToggle from '@/components/ThemeToggle';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/components/ThemeProvider';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";

const Header: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme } = useTheme();

  const [isLoggedIn] = useState<boolean>(true); // Always show profile icon

  // Use startsWith for optimizer highlighting, strict match for dashboard/knowledge
  const isOptimizerTab = location.pathname.startsWith('/optimizer') || location.pathname.startsWith('/industry-flow');
  const isDashboard = location.pathname === '/dashboard';
  const hideLoginButton = isLoggedIn || isDashboard;

  return (
    <header className={`w-full py-6 px-4 md:px-8 border-b ${theme === 'dark' ? 'border-recrea-card-border' : 'border-recrea-mint'}`}>
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <Logo className="animate-fade-in" />

        <div className="flex items-center gap-6">
          {/* Navigation Items with Tooltips */}
          <nav className="hidden md:flex items-center space-x-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={() => navigate('/dashboard')}
                    variant="link"
                    className={`
                      font-medium
                      ${isDashboard
                        ? 'text-recrea-turquoise border-b-2 border-recrea-turquoise font-bold animate-fade-in'
                        : (theme === 'dark' ? 'text-white' : 'text-recrea-dark')}
                      hover:text-recrea-turquoise hover:bg-transparent
                      pb-1 flex items-center gap-1
                    `}
                  >
                    Knowledge Hub
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>Get curated best in class knowledge on climate risk for your sector.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={() => navigate('/optimizer')}
                    variant="link" 
                    className={`
                      font-medium
                      ${isOptimizerTab
                        ? 'text-recrea-teal border-b-2 border-recrea-teal font-bold animate-fade-in'
                        : (theme === 'dark' ? 'text-white' : 'text-recrea-dark')}
                      hover:text-recrea-teal hover:bg-transparent
                      transition-all
                      flex items-center gap-1
                    `}
                    style={{
                      transition: 'border-color 0.2s, color 0.2s, font-weight 0.2s',
                    }}
                  >
                    Optimizer
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>Use our patented AI solution AIde™ to arrive at financial risk under alternate scenarios.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={() => navigate('/marketplace')}
                    variant="link" 
                    className={`font-medium ${theme === 'dark' ? 
                      'text-white' : 
                      'text-recrea-dark'} hover:text-recrea-turquoise hover:bg-transparent flex items-center gap-1`}
                  >
                    Marketplace
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <p>Connect with industry experts and technology providers to facilitate implementation.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </nav>
          
          <div className="flex items-center gap-4 animate-fade-in">
            <ThemeToggle />
            <Avatar 
              className="cursor-pointer hover:ring-2 hover:ring-recrea-turquoise transition-all"
              onClick={() => navigate('/profile')}
            >
              <AvatarFallback>
                <UserRound className="h-5 w-5" />
              </AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
