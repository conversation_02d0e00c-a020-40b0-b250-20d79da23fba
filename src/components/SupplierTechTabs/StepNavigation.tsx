
import React from "react";
import { Step } from "./types";

interface StepNavigationProps {
  steps: Step[];
  currentStepIndex: number;
}

export const StepNavigation: React.FC<StepNavigationProps> = ({
  steps,
  currentStepIndex
}) => {
  return (
    <div className="flex justify-between mb-6">
      {steps.map((step, index) => (
        <div
          key={step.id}
          className={`flex flex-col items-center ${
            index <= currentStepIndex ? "text-blue-600" : "text-gray-400"
          }`}
        >
          <div
            className={`rounded-full w-8 h-8 flex items-center justify-center mb-1 ${
              index < currentStepIndex 
                ? "bg-blue-100 border border-blue-600" 
                : index === currentStepIndex
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100"
            }`}
          >
            <span className="text-sm">
              {index < currentStepIndex ? "✓" : index + 1}
            </span>
          </div>
          <span className="text-xs text-center hidden sm:block">{step.label}</span>
        </div>
      ))}
    </div>
  );
};
