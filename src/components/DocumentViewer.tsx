import React from 'react';
import { FileText, Upload, Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface DocumentViewerProps {
  selectedFile: string | null;
  onFileUpload: (file: File) => void;
  documentUrl?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ 
  selectedFile, 
  onFileUpload,
  documentUrl 
}) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onFileUpload(e.target.files[0]);
    }
  };

  if (!selectedFile) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        <div className="max-w-md w-full">
          <div className="flex flex-col items-center justify-center p-10 border-2 border-dashed border-primary/30 rounded-xl bg-background/40 hover:bg-background/60 transition-colors">
            <div className="bg-primary/10 p-6 rounded-full mb-6">
              <Upload size={42} className="text-primary animate-pulse-green" />
            </div>
            <h3 className="text-xl font-medium mb-2 text-center">Upload your document</h3>
            <p className="mb-6 text-center text-muted-foreground">Drag and drop a PDF file or click below to browse</p>
            <Button 
              variant="outline" 
              className="text-primary border-primary hover:bg-primary/10 font-medium px-6"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload size={18} className="mr-2" />
              Choose files
            </Button>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              hidden
              accept=".pdf,.txt,.doc,.docx"
            />
            <p className="mt-6 text-xs text-muted-foreground text-center">
              Supported file formats: PDF, TXT, DOC, DOCX (Max size: 10MB)
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="flex-none flex items-center justify-between bg-muted/30 dark:bg-background/30 border-b p-3">
        <div className="flex items-center space-x-2">
          <div className="bg-primary/10 p-1 rounded">
            <FileText size={18} className="text-primary" />
          </div>
          <span className="font-medium">{selectedFile}</span>
        </div>
        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground hover:text-foreground">
            <Pencil size={16} />
          </Button>
        </div>
      </div>
      <div className="flex-1 bg-muted/30 dark:bg-background/20 p-6 overflow-auto">
        {documentUrl ? (
          <div className="h-full">
            <iframe 
              src={documentUrl} 
              className="w-full h-full min-h-[300px] rounded-lg border border-border/60"
              title="Document Preview"
              style={{ height: '100%', minHeight: '100%' }}
            />
          </div>
        ) : (
          <Card className="rounded-lg p-8 min-h-[300px] shadow-sm border border-border/60">
            <h3 className="text-lg font-semibold mb-4">Document Content</h3>
            <p className="text-muted-foreground">
              Loading document preview...
            </p>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DocumentViewer;
