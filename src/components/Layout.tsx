
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import AppSidebar from "@/components/AppSidebar";
import OptimizerSidebar from "@/components/OptimizerSidebar";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { theme } = useTheme();
  const location = useLocation();

  // For optimizer context, let the page handle its own sidebar
  const isOptimizerContext = location.pathname === '/optimizer' || location.pathname.startsWith('/industry-flow');

  return (
    <div className={`min-h-screen flex w-full ${theme === 'dark' ? 'bg-recrea-dark text-white' : 'bg-white text-recrea-dark'}`}>
      <SidebarProvider>
        {!isOptimizerContext && <AppSidebar />}
        <div className="flex flex-col w-full">
          <Header />
          <div className="flex-1">
            {children}
          </div>
          <Footer />
        </div>
      </SidebarProvider>
    </div>
  );
};

export default Layout;
