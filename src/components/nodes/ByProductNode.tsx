
import React from 'react';
import { <PERSON>le, Position } from '@xyflow/react';

interface ByProductNodeProps {
  data: {
    label: string;
  };
}

export const ByProductNode: React.FC<ByProductNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-purple-100 border-2 border-purple-400">
      <Handle type="target" position={Position.Top} />
      <div className="font-bold">{data.label}</div>
    </div>
  );
};
