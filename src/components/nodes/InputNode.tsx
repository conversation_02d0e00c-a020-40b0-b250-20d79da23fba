
import React from 'react';
import { <PERSON>le, Position } from '@xyflow/react';

interface InputNodeProps {
  data: {
    label: string;
  };
}

export const InputNode: React.FC<InputNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-blue-100 border-2 border-blue-400">
      <div className="font-bold">{data.label}</div>
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};
