
import React from 'react';
import { Handle, Position } from '@xyflow/react';

interface OutputNodeProps {
  data: {
    label: string;
  };
}

export const OutputNode: React.FC<OutputNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-green-100 border-2 border-green-400">
      <Handle type="target" position={Position.Top} />
      <div className="font-bold">{data.label}</div>
    </div>
  );
};
