
import React from 'react';
import { Handle, Position } from '@xyflow/react';

interface IndustryNodeProps {
  data: {
    label: string;
  };
}

export const IndustryNode: React.FC<IndustryNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-white border-2 border-stone-400">
      <Handle type="target" position={Position.Top} />
      <div className="font-bold">{data.label}</div>
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};
