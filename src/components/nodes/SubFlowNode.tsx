
import React from 'react';
import { Handle, Position } from '@xyflow/react';

interface SubFlowNodeProps {
  data: {
    label: string;
  };
}

export const SubFlowNode: React.FC<SubFlowNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-md bg-yellow-100 border-2 border-yellow-400">
      <Handle type="target" position={Position.Top} />
      <div className="font-bold">{data.label}</div>
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
};
