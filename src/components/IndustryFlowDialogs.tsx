import React from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import * as z from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { ActivitySelector } from './ActivitySelector';
import { useParams } from 'react-router-dom';

// Node editing schema
const nodeSchema = z.object({
  label: z.string().min(1, {
    message: "Node name is required"
  })
});

// Edge editing schema
const edgeSchema = z.object({
  label: z.string().optional()
});

interface EditNodeDialogProps {
  editNode: {
    id: string;
    label: string;
  } | null;
  setEditNode: (node: {
    id: string;
    label: string;
  } | null) => void;
  handleUpdateNodeName: (nodeId: string, newLabel: string) => void;
  industryId?: string;
  existingNodeNames?: string[];
}

interface EditEdgeDialogProps {
  editEdge: {
    id: string;
    label: string;
    data?: any;
  } | null;
  setEditEdge: (edge: {
    id: string;
    label: string;
    data?: any;
  } | null) => void;
  handleUpdateEdgeName: () => void;
}

export const EditNodeDialog: React.FC<EditNodeDialogProps> = ({
  editNode,
  setEditNode,
  handleUpdateNodeName,
  industryId,
  existingNodeNames = []
}) => {
  const { industryId: paramIndustryId } = useParams<{ industryId: string }>();

  // Use industryId from props or params
  const currentIndustryId = industryId || paramIndustryId || 'default';

  // Handle activity selection from ActivitySelector
  const handleActivitySelect = (activityName: string) => {
    if (editNode) {
      handleUpdateNodeName(editNode.id, activityName);
      setEditNode(null);
    }
  };

  const handleClose = () => {
    setEditNode(null);
  };

  return (
    <Dialog open={!!editNode} onOpenChange={(open) => {
      if (!open) {
        handleClose();
      }
    }}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Activity</DialogTitle>
          <DialogDescription>
            Select an activity for this node. You can choose from existing activities or create a new one.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <ActivitySelector
            industryId={currentIndustryId}
            onActivitySelect={handleActivitySelect}
            onCancel={handleClose}
            placeholder="Select an activity for this node..."
            label="Node Activity"
            selectedActivity={editNode?.label || ''}
            existingNodeNames={existingNodeNames}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const EditEdgeDialog: React.FC<EditEdgeDialogProps> = ({
  editEdge,
  setEditEdge,
  handleUpdateEdgeName
}) => {
  const form = useForm<z.infer<typeof edgeSchema>>({
    resolver: zodResolver(edgeSchema),
    defaultValues: {
      label: ''
    }
  });

  // Reset and set form values when editEdge changes
  React.useEffect(() => {
    if (editEdge) {
      form.reset({
        label: editEdge.label || ''
      });
    }
  }, [editEdge, form]);

  // Handle form submission
  const onSubmit = (values: z.infer<typeof edgeSchema>) => {
    if (editEdge) {
      setEditEdge({
        ...editEdge,
        label: values.label || ''
      });
      handleUpdateEdgeName();
      // Close the dialog
      setEditEdge(null);
      form.reset();
    }
  };

  const handleClose = () => {
    setEditEdge(null);
    form.reset({
      label: ''
    });
  };

  // Render connection details based on edge data
  const renderConnectionDetails = () => {
    if (!editEdge || !editEdge.data) return null;
    
    const { data } = editEdge;
    const outputType = data.outputType || "";
    
    let detailsContent = null;
    
    if (outputType.includes('energy')) {
      detailsContent = (
        <div className="bg-green-50 p-3 rounded-md mb-4 text-sm">
          <h4 className="font-medium mb-2">Energy Details</h4>
          <div className="grid grid-cols-2 gap-2">
            {data.quantity && (
              <>
                <span className="text-gray-500">Quantity:</span>
                <span>{data.quantity} {data.unit || ''}</span>
              </>
            )}
            
            {data.technology && (
              <>
                <span className="text-gray-500">Technology:</span>
                <span>{data.technology}</span>
              </>
            )}
            
            {data.sec && (
              <>
                <span className="text-gray-500">SEC:</span>
                <span>{data.sec}</span>
              </>
            )}
            
            {data.other?.replaced && (
              <>
                <span className="text-gray-500">Replaces:</span>
                <span>{data.other.replaced}</span>
              </>
            )}
          </div>
        </div>
      );
    } else if (outputType.includes('material')) {
      detailsContent = (
        <div className="bg-purple-50 p-3 rounded-md mb-4 text-sm">
          <h4 className="font-medium mb-2">Material Details</h4>
          <div className="grid grid-cols-2 gap-2">
            {data.quantity && (
              <>
                <span className="text-gray-500">Quantity:</span>
                <span>{data.quantity} {data.unit || ''}</span>
              </>
            )}
            
            {data.technology && (
              <>
                <span className="text-gray-500">Technology:</span>
                <span>{data.technology}</span>
              </>
            )}
            
            {data.smc && (
              <>
                <span className="text-gray-500">SMC:</span>
                <span>{data.smc}</span>
              </>
            )}
            
            {data.other?.emissionFactor && (
              <>
                <span className="text-gray-500">Emission Factor:</span>
                <span>{data.other.emissionFactor} {data.other.emissionUnit || ''}</span>
              </>
            )}
          </div>
        </div>
      );
    }
    
    return detailsContent;
  };
  
  return (
    <Dialog open={!!editEdge} onOpenChange={(open) => {
      if (!open) {
        handleClose();
      }
    }}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Connection Name</DialogTitle>
          <DialogDescription>Enter a label for this connection.</DialogDescription>
        </DialogHeader>
        
        {renderConnectionDetails()}
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField 
              control={form.control} 
              name="label" 
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Connection name</FormLabel>
                  <FormControl>
                    <Input 
                      {...field} 
                      placeholder="Enter connection name" 
                      autoFocus 
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          form.handleSubmit(onSubmit)();
                        }
                      }} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} 
            />
            <div className="flex gap-2">
              <Button type="submit">Update Name</Button>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
