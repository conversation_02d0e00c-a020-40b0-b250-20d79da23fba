
import { TechnologyResponse, MaterialResponse, EnergyResponse, EmissionResponse } from '@/components/ConnectionForm/types';
import { ActivityResponse } from '@/services/activitiesApi';

// Base API URL - using the same pattern as other services
const API_BASE_URL = "https://api.recre8.earth";

// Helper function to get auth headers
const getAuthHeaders = () => {
  const accessToken = localStorage.getItem('accessToken');
  if (!accessToken) {
    throw new Error('No access token found. Please log in again.');
  }
  
  return {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json'
  };
};

// Updated interface for toast utilities to match useToast hook
interface ToastUtils {
  toast: (options: {
    title: string;
    description: string;
    variant: "default" | "destructive";
  }) => void;
}

/**
 * Fetch activities for a specific sector and find activity UUID by name
 */
export const findActivityUuidByName = async (
  sectorUuid: string,
  activityName: string,
  toastUtils?: ToastUtils
): Promise<string | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/activities`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch activities: ${response.status} ${response.statusText}`);
    }

    const activities: ActivityResponse[] = await response.json();
    const foundActivity = activities.find(activity => activity.name === activityName);

    return foundActivity ? foundActivity.uuid : null;
  } catch (error) {
    console.error('Error finding activity UUID by name:', error);

    if (toastUtils) {
      toastUtils.toast({
        title: "Error Finding Activity",
        description: error instanceof Error ? error.message : "Failed to find activity",
        variant: "destructive"
      });
    }

    return null;
  }
};

/**
 * Fetch technologies for a specific activity
 */
export const fetchTechnologies = async (
  activityUuid: string,
  toastUtils?: ToastUtils
): Promise<TechnologyResponse[]> => {
  console.log('fetchTechnologies: Called with activityUuid:', activityUuid);

  try {
    const url = `${API_BASE_URL}/resources/activities/${activityUuid}/technologies`;
    console.log('fetchTechnologies: Making request to:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      console.error('fetchTechnologies: Request failed with status:', response.status, response.statusText);
      throw new Error(`Failed to fetch technologies: ${response.status} ${response.statusText}`);
    }

    const technologies: TechnologyResponse[] = await response.json();
    console.log('fetchTechnologies: Successfully fetched technologies:', technologies);
    return technologies;
  } catch (error) {
    console.error('Error fetching technologies:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Technologies",
        description: error instanceof Error ? error.message : "Failed to load technologies from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch technologies for a specific activity
 */
export const fetchActivityNameTechnologies = async (
  activityName: string,
  toastUtils?: ToastUtils
): Promise<TechnologyResponse[]> => {
  console.log('fetchTechnologies: Called with activityName:', activityName);

  try {
    const url = `${API_BASE_URL}/resources/activities/name/${activityName}/technologies`;
    console.log('fetchTechnologies: Making request to:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      console.error('fetchTechnologies: Request failed with status:', response.status, response.statusText);
      throw new Error(`Failed to fetch technologies: ${response.status} ${response.statusText}`);
    }

    const technologies: TechnologyResponse[] = await response.json();
    console.log('fetchTechnologies: Successfully fetched technologies:', technologies);
    return technologies;
  } catch (error) {
    console.error('Error fetching technologies:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Technologies",
        description: error instanceof Error ? error.message : "Failed to load technologies from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Create a new technology
 */
export const createTechnology = async (
  technologyData: { name: string; description?: string },
  toastUtils?: ToastUtils
): Promise<TechnologyResponse | null> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/technologies`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(technologyData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create technology: ${response.status} ${response.statusText}`);
    }

    const newTechnology: TechnologyResponse = await response.json();

    if (toastUtils) {
      toastUtils.toast({
        title: "Technology Created",
        description: `Successfully created "${newTechnology.name}"`,
        variant: "default"
      });
    }

    return newTechnology;
  } catch (error) {
    console.error('Error creating technology:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Technology",
        description: error instanceof Error ? error.message : "Failed to create technology",
        variant: "destructive"
      });
    }
    
    return null;
  }
};

/**
 * Associate a technology with an activity
 */
export const associateTechnologyWithActivity = async (
  activityUuid: string,
  technologyUuid: string,
  toastUtils?: ToastUtils
): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/activities/${activityUuid}/technologies/${technologyUuid}`, {
      method: 'POST',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to associate technology with activity: ${response.status} ${response.statusText}`);
    }

    if (toastUtils) {
      toastUtils.toast({
        title: "Technology Associated",
        description: "Successfully associated technology with activity",
        variant: "default"
      });
    }

    return true;
  } catch (error) {
    console.error('Error associating technology with activity:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Associating Technology",
        description: error instanceof Error ? error.message : "Failed to associate technology with activity",
        variant: "destructive"
      });
    }
    
    return false;
  }
};

/**
 * Fetch materials for a specific sector
 */
export const fetchMaterials = async (
  sectorUuid: string,
  toastUtils?: ToastUtils
): Promise<MaterialResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/sectors/${sectorUuid}/materials`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch materials: ${response.status} ${response.statusText}`);
    }

    const materials: MaterialResponse[] = await response.json();
    return materials;
  } catch (error) {
    console.error('Error fetching materials:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Materials",
        description: error instanceof Error ? error.message : "Failed to load materials from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch all energies
 */
export const fetchEnergies = async (
  toastUtils?: ToastUtils
): Promise<EnergyResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/energies`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch energies: ${response.status} ${response.statusText}`);
    }

    const energies: EnergyResponse[] = await response.json();
    return energies;
  } catch (error) {
    console.error('Error fetching energies:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Energies",
        description: error instanceof Error ? error.message : "Failed to load energies from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};

/**
 * Fetch all emissions
 */
export const fetchEmissions = async (
  toastUtils?: ToastUtils
): Promise<EmissionResponse[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/resources/emissions`, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch emissions: ${response.status} ${response.statusText}`);
    }

    const emissions: EmissionResponse[] = await response.json();
    return emissions;
  } catch (error) {
    console.error('Error fetching emissions:', error);
    
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Loading Emissions",
        description: error instanceof Error ? error.message : "Failed to load emissions from server",
        variant: "destructive"
      });
    }
    
    // Return empty array as fallback
    return [];
  }
};
