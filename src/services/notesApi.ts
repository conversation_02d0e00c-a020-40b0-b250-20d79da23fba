import { Note, NoteCreate, NoteUpdate, NotesListResponse } from "@/types/Note";
import { useToast } from "@/hooks/use-toast";

interface ToastUtils {
  toast: ReturnType<typeof useToast>["toast"];
  dismiss: ReturnType<typeof useToast>["dismiss"];
  toasts: ReturnType<typeof useToast>["toasts"];
}

// Make sure the API URL ends with a trailing slash to prevent 307 redirects
const API_URL = "https://api.recre8.earth/notes/";

// Helper function to handle API requests with automatic redirect following
const fetchWithRedirects = async (url: string, options: RequestInit): Promise<Response> => {
  const response = await fetch(url, {
    ...options,
    redirect: 'follow' // Explicitly follow redirects
  });
  return response;
};

export const fetchNotes = async (toastUtils?: ToastUtils): Promise<Note[]> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const response = await fetch(API_URL, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch notes: ${response.status}`);
    }

    const data: NotesListResponse = await response.json();
    
    // Convert string dates to Date objects
    return data.items.map(note => ({
      ...note,
      created_at: new Date(note.created_at),
      updated_at: note.updated_at ? new Date(note.updated_at) : null
    }));
  } catch (error: any) {
    console.error("Notes fetch error:", error);
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Fetching Notes",
        description: error.message || "Failed to fetch notes",
        variant: "destructive"
      });
    }
    return [];
  }
};

export const fetchNote = async (noteUuid: string, toastUtils?: ToastUtils): Promise<Note | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    // Note the lack of trailing slash here since we're appending the UUID
    const response = await fetch(`${API_URL}${noteUuid}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch note: ${response.status}`);
    }

    const note: Note = await response.json();
    
    // Convert string dates to Date objects
    return {
      ...note,
      created_at: new Date(note.created_at),
      updated_at: note.updated_at ? new Date(note.updated_at) : null
    };
  } catch (error: any) {
    console.error("Note fetch error:", error);
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Fetching Note",
        description: error.message || "Failed to fetch note details",
        variant: "destructive"
      });
    }
    return null;
  }
};

export const createNote = async (noteData: NoteCreate, toastUtils?: ToastUtils): Promise<Note | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(noteData)
    });

    if (!response.ok) {
      throw new Error(`Failed to create note: ${response.status}`);
    }

    const note: Note = await response.json();
    
    // Convert string dates to Date objects
    return {
      ...note,
      created_at: new Date(note.created_at),
      updated_at: note.updated_at ? new Date(note.updated_at) : null
    };
  } catch (error: any) {
    console.error("Note creation error:", error);
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Creating Note",
        description: error.message || "Failed to create note",
        variant: "destructive"
      });
    }
    return null;
  }
};

export const updateNote = async (noteUuid: string, noteData: NoteUpdate, toastUtils?: ToastUtils): Promise<Note | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const response = await fetch(`${API_URL}${noteUuid}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(noteData)
    });

    if (!response.ok) {
      throw new Error(`Failed to update note: ${response.status}`);
    }

    const note: Note = await response.json();
    
    // Convert string dates to Date objects
    return {
      ...note,
      created_at: new Date(note.created_at),
      updated_at: note.updated_at ? new Date(note.updated_at) : null
    };
  } catch (error: any) {
    console.error("Note update error:", error);
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Updating Note",
        description: error.message || "Failed to update note",
        variant: "destructive"
      });
    }
    return null;
  }
};

export const deleteNote = async (noteUuid: string, toastUtils?: ToastUtils): Promise<boolean> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      throw new Error('No access token found. Please log in again.');
    }

    const response = await fetch(`${API_URL}${noteUuid}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to delete note: ${response.status}`);
    }

    return true;
  } catch (error: any) {
    console.error("Note deletion error:", error);
    if (toastUtils) {
      toastUtils.toast({
        title: "Error Deleting Note",
        description: error.message || "Failed to delete note",
        variant: "destructive"
      });
    }
    return false;
  }
}; 