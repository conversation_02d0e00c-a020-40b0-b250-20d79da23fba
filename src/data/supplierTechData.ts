import { exampleSupplierTechTabsData } from "@/data/exampleSupplierTechTabsData";

export const supplierTechData: Record<string, any> = {
  'Natural Gas': {
  technology: 'Boiler',
  energyInputs: [
    {
      id: '1',
      name: 'Natural Gas',
      unit: 'GJ',
      costPerUnit: '10',
      sec: '5',
      technology: 'Nil'
    }
  ],
  emissions: [
    {
      id: '1',
      name: 'CO2',
      emissionFactor: '0.2',
      unit: 'tonnes'
    }
  ],
  materialInputs: [
    {
      id: '1',
      name: 'Water',
      unit: 'm3',
      costPerUnit: '1'
    }
  ],
  energyOutputs: [
    {
      id: '1',
      name: 'Steam',
      unit: 'GJ',
      smc: '0.9',
      isFinalOutput: true,
      finalOutputQuantity: '100',
      finalOutputUnit: 'GJ'
    }
  ],
  materialOutputs: [
    {
      id: '1',
      name: 'Flue Gas',
      unit: 'm3',
      smc: '1.2',
      isFinalOutput: false,
      finalOutputQuantity: '',
      finalOutputUnit: ''
    }
  ],
  energyByProducts: [
    {
      id: '1',
      name: 'Waste Heat',
      unit: 'GJ',
      bppo: '0.1',
      energyReplaced: 'Electricity'
    }
  ],
  materialByProducts: [
    {
      id: '1',
      name: 'Ash',
      unit: 'Tonnes',
      bppo: '0.05'
    }
  ],
  financialData: {
    capacity: '100',
    capacityUnit: 'Tonnes/day',
    capitalCostPerUnit: '1000',
    annualOMCost: '50000'
  }
  },
  Electricity: {
    technology: 'Electric Heater',
    energyInputs: [
      {
        id: '1',
        name: 'Electricity',
        unit: 'kWh',
        costPerUnit: '0.15',
        sec: '1',
        technology: 'Grid'
      }
    ],
    emissions: [],
    materialInputs: [],
    energyOutputs: [
      {
        id: '1',
        name: 'Heat',
        unit: 'GJ',
        smc: '1',
        isFinalOutput: true,
        finalOutputQuantity: '90',
        finalOutputUnit: 'GJ'
      }
    ],
    materialOutputs: [],
    energyByProducts: [],
    materialByProducts: [],
    financialData: {
      capacity: '90',
      capacityUnit: 'GJ/day',
      capitalCostPerUnit: '500',
      annualOMCost: '10000'
    }
  },
  Biomass: {
    technology: 'Biomass Boiler',
    energyInputs: [
      {
        id: '1',
        name: 'Biomass',
        unit: 'Tonnes',
        costPerUnit: '50',
        sec: '8',
        technology: 'Nil'
      }
    ],
    emissions: [
      {
        id: '1',
        name: 'CO2',
        emissionFactor: '0.1',
        unit: 'tonnes'
      }
    ],
    materialInputs: [
      {
        id: '1',
        name: 'Water',
        unit: 'm3',
        costPerUnit: '1'
      }
    ],
    energyOutputs: [
      {
        id: '1',
        name: 'Steam',
        unit: 'GJ',
        smc: '0.8',
        isFinalOutput: true,
        finalOutputQuantity: '80',
        finalOutputUnit: 'GJ'
      }
    ],
    materialOutputs: [
      {
        id: '1',
        name: 'Flue Gas',
        unit: 'm3',
        smc: '1.5',
        isFinalOutput: false,
        finalOutputQuantity: '',
        finalOutputUnit: ''
      }
    ],
    energyByProducts: [
      {
        id: '1',
        name: 'Waste Heat',
        unit: 'GJ',
        bppo: '0.15',
        energyReplaced: 'Electricity'
      }
    ],
    materialByProducts: [
      {
        id: '1',
        name: 'Ash',
        unit: 'Tonnes',
        bppo: '0.1'
      }
    ],
    financialData: {
      capacity: '80',
      capacityUnit: 'Tonnes/day',
      capitalCostPerUnit: '1200',
      annualOMCost: '60000'
    }
  }
};

export const dummyConnectionData = supplierTechData['Natural Gas'];

function mapExampleDataToDialogState(data) {
  // Extract technologies
  const technologies = data.technologies.map(t => t.name);

  // Map outputs
  const outputs = data.technologies.map((tech, idx) => ({
    id: `output-${idx}`,
    targetNode: tech.outputs.materials[0]?.targetNodeId || "",
    outputTechnology: tech.name,
    energyOutputs: tech.outputs.energies || [],
    matOutputs: tech.outputs.materials || []
  }));

  // Map technology form data
  const technologyFormData = {};
  data.technologies.forEach(tech => {
    technologyFormData[tech.name] = {
      technology: tech.name,
      startYear: tech.startYear?.toString() || "",
      endYear: tech.endYear?.toString() || "",
      // Map inputs
      energyInputs: tech.inputs.energies || [],
      materialInputs: tech.inputs.materials || [],
      emissions: tech.inputs.emissions || [],
      // By-products
      energyByProducts: tech.byproducts.energies || [],
      materialByProducts: tech.byproducts.materials || [],
      // Financial
      financial: {
        capacity: tech.financial.capacity?.toString() || "",
        capacityUnit: tech.financial.capacityUnit || "",
        capitalCost: tech.financial.capitalCost?.toString() || "",
        capitalCostUnit: tech.financial.capitalCostUnit || "",
        omCost: tech.financial.operatingMaintenanceCost?.toString() || "",
        omCostUnit: tech.financial["operatingM YaintenanceCostUnit"] || ""
      }
    };
  });

  // Pick the first technology as active
  const activeTechnology = technologies[0];

  // Compose formData (for backward compatibility)
  const formData = {
    activity: data.activity,
    technology: activeTechnology,
    startYear: data.technologies[0].startYear?.toString() || "",
    endYear: data.technologies[0].endYear?.toString() || "",
    // ...add other fields as needed
  };

  return {
    technologies,
    outputs,
    technologyFormData,
    activeTechnology,
    formData
  };
}

const {
  technologies,
  outputs,
  technologyFormData,
  activeTechnology,
  formData
} = mapExampleDataToDialogState(exampleSupplierTechTabsData);

// Use these as initial state in your SupplierTechDialog 