// Example data for Supplier Tech Tabs (read-only display)
// This structure matches the expected input for Supplier Tech Tabs components

export const exampleSupplierTechTabsData = {
  activity: "Cryogenic Seperation",
  position: {
    x: 150,
    y: 370
  },
  technologies: [
    {
      name: "Boiler",
      inputs: {
        energies: [],
        emissions: [],
        materials: [
          {
            id: "material-input-0",
            cost: 1,
            unit: "Tonnes",
            material: "Processed gas",
            quantity: 1,
            sourceNodeId: "7",
            sourceTechnology: "Boiler",
            specificMaterialCost: 1
          }
        ]
      },
      endYear: 2035,
      outputs: {
        energies: [],
        materials: [
          {
            id: "material-output-0",
            unit: "Tonnes",
            material: "Natural gas",
            quantity: 1,
            targetNodeId: "natural-gas",
            isFinalOutput: true,
            targetTechnology: "Boiler"
          }
        ]
      },
      isCustom: false,
      financial: {
        capacity: 1,
        capitalCost: 0,
        capacityUnit: "Units/day",
        capitalCostUnit: "1",
        operatingMaintenanceCost: 1,
        "operatingM YaintenanceCostUnit": "USD/year"
      },
      startYear: 2025,
      byproducts: {
        energies: [],
        materials: []
      }
    }
  ]
}; 