import React, { useState, useEffect } from 'react';
import NotesList from '@/components/NotesComponents/NotesList';
import NoteEditor from '@/components/NotesComponents/NoteEditor';
import WelcomePage from '@/components/NotesComponents/WelcomePage';
import { Note, NoteType } from '@/types/Note';
import { useToast } from '@/hooks/use-toast';
import { fetchNotes, createNote, updateNote, deleteNote } from '@/services/notesApi';

const NotesCreator: React.FC = () => {
  const toast = useToast();
  const [notes, setNotes] = useState<Note[]>([]);
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load notes from API on component mount
  useEffect(() => {
    const loadNotes = async () => {
      setIsLoading(true);
      try {
        const notesData = await fetchNotes({
          toast: toast.toast,
          dismiss: toast.dismiss,
          toasts: toast.toasts
        });
        setNotes(notesData);
        // Select the first note if available
        if (notesData.length > 0 && !selectedNoteId) {
          setSelectedNoteId(notesData[0].uuid);
        }
      } catch (error) {
        console.error('Failed to load notes', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadNotes();
  }, []);

  const handleCreateNewNote = async () => {
    const newNoteData = {
      title: 'Untitled',
      content: '',
      note_type: NoteType.GENERAL,
      is_pinned: false
    };
    
    const createdNote = await createNote(newNoteData, {
      toast: toast.toast,
      dismiss: toast.dismiss,
      toasts: toast.toasts
    });
    
    if (createdNote) {
      setNotes([createdNote, ...notes]);
      setSelectedNoteId(createdNote.uuid);
      
      toast.toast({
        title: "New note created",
        description: "Your new note has been created",
      });
    }
  };

  const handleSaveNote = async (updatedNote: Note) => {
    // Check if there's already a note with the same title (excluding the current note)
    const duplicateNote = notes.find(note => 
      note.title.toLowerCase() === updatedNote.title.toLowerCase() && 
      note.uuid !== updatedNote.uuid
    );
    
    if (duplicateNote) {
      toast.toast({
        title: "Duplicate Note Title",
        description: "A note with this title already exists. Please choose a different title.",
        variant: "destructive"
      });
      return;
    }
    
    const noteData = {
      title: updatedNote.title,
      content: updatedNote.content,
      note_type: updatedNote.note_type,
      is_pinned: updatedNote.is_pinned,
      sector_uuid: updatedNote.sector_uuid
    };
    
    const savedNote = await updateNote(updatedNote.uuid, noteData, {
      toast: toast.toast,
      dismiss: toast.dismiss,
      toasts: toast.toasts
    });
    
    if (savedNote) {
      setNotes(notes.map(note => 
        note.uuid === savedNote.uuid ? savedNote : note
      ));
    }
  };

  const handleDeleteNote = async (id: string) => {
    const success = await deleteNote(id, {
      toast: toast.toast,
      dismiss: toast.dismiss,
      toasts: toast.toasts
    });
    
    if (success) {
      setNotes(notes.filter(note => note.uuid !== id));
      
      if (selectedNoteId === id) {
        setSelectedNoteId(notes.length > 1 ? notes.find(note => note.uuid !== id)?.uuid || null : null);
      }
      
      toast.toast({
        title: "Note deleted",
        description: "Your note has been deleted",
        variant: "destructive",
      });
    }
  };

  const selectedNote = notes.find(note => note.uuid === selectedNoteId) || null;

  if (isLoading) {
    return (
      <div className="flex h-[calc(100vh-160px)] items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex h-[calc(100vh-160px)] overflow-hidden">
      <NotesList
        notes={notes}
        selectedNoteId={selectedNoteId}
        onSelectNote={setSelectedNoteId}
        onCreateNewNote={handleCreateNewNote}
        onDeleteNote={handleDeleteNote}
      />
      
      {notes.length === 0 ? (
        <WelcomePage onCreateNewNote={handleCreateNewNote} />
      ) : (
        <NoteEditor 
          selectedNote={selectedNote} 
          onUpdateNote={handleSaveNote}
          onSaveNote={handleSaveNote}
        />
      )}
    </div>
  );
};

export default NotesCreator;
