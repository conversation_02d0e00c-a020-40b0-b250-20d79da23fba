import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { 
  BarChart, 
  Zap, 
  Leaf, 
  FileText, 
  AlertCircle,
  Check,
  ChevronDown
} from 'lucide-react';
import { 
  DropdownMenu, 
  DropdownMenuTrigger, 
  DropdownMenuContent, 
  DropdownMenuLabel, 
  DropdownMenuItem, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import PerformanceTab from '@/components/dashboard/PerformanceTab';
import MaterialTopicsTab from '@/components/dashboard/MaterialTopicsTab';
import ClimateRisksTab from '@/components/dashboard/ClimateRisksTab';
import TechnologiesTab from '@/components/dashboard/TechnologiesTab';
import RegulatoryTab from '@/components/dashboard/RegulatoryTab';
import { useSectors } from '@/hooks/useSectors';

// Define missing types
interface ClimateRisk {
  id: number;
  title: string;
  category: string;
  type: string;
  description: string;
  severity: string;
}

interface Technology {
  id: number;
  title: string;
  description: string;
  maturity: string;
  impact: string;
}

// Mock API functions that were referenced but not defined
const fetchMaterialTopicsData = async (sectorName: string) => {
  console.log("Fetching material topics for sector:", sectorName);
  // In a real implementation, this would make an API call
  // For now, return the static data
  return materialTopicsData;
};

const fetchClimateRisks = async (sectorName: string, toastApi: any) => {
  console.log("Fetching climate risks for sector:", sectorName);
  // In a real implementation, this would make an API call
  // For now, return the static data
  return climateRisksData;
};

const fetchTechnologies = async (sectorName: string, toastApi: any) => {
  console.log("Fetching technologies for sector:", sectorName);
  // In a real implementation, this would make an API call
  // For now, return the static data
  return technologiesData;
};

const materialTopicsData = [
  {
    id: 1,
    title: "Greenhouse Gas Emissions",
    description: "Direct and indirect emissions from operations, including Scope 1, 2, and 3 emissions across the value chain.",
    relevance: "High",
    icon: <Leaf className="h-5 w-5 text-recrea-turquoise" />
  },
  {
    id: 2,
    title: "Energy Management",
    description: "Energy consumption, efficiency measures, and transition to renewable energy sources.",
    relevance: "High",
    icon: <Zap className="h-5 w-5 text-recrea-turquoise" />
  },
  {
    id: 3,
    title: "Water & Wastewater",
    description: "Water consumption, discharge quality, and water stress in operating regions.",
    relevance: "Medium",
    icon: <Zap className="h-5 w-5 text-recrea-turquoise" />
  },
  {
    id: 4,
    title: "Resource Efficiency",
    description: "Material use optimization, circular economy practices, and waste management strategies.",
    relevance: "Medium",
    icon: <Zap className="h-5 w-5 text-recrea-turquoise" />
  }
];

const climateRisksData = [
  {
    id: 1,
    title: "Carbon Pricing Exposure",
    category: "Transitional",
    type: "Regulatory",
    description: "Increasing costs due to carbon taxes and emissions trading schemes across operating regions.",
    severity: "High"
  },
  {
    id: 2,
    title: "Supply Chain Disruptions",
    category: "Physical",
    type: "Operational",
    description: "Extreme weather events disrupting raw material supply and logistics networks.",
    severity: "Medium"
  },
  {
    id: 3,
    title: "Technology Transition Risks",
    category: "Transitional",
    type: "Strategic",
    description: "Risk of assets becoming stranded due to rapid technology shifts toward low-carbon alternatives.",
    severity: "Medium"
  },
  {
    id: 4,
    title: "Climate-related Litigation",
    category: "Transitional",
    type: "Reputational",
    description: "Increasing legal challenges related to climate change impacts and disclosure adequacy.",
    severity: "Low"
  }
];

const technologiesData = [
  {
    id: 1,
    title: "Hydrogen-based Direct Reduction",
    description: "Uses green hydrogen to reduce iron ore instead of coal, potentially eliminating CO2 emissions from the reduction process.",
    maturity: "Pilot",
    impact: "Transformative"
  },
  {
    id: 2,
    title: "CCUS in Blast Furnaces",
    description: "Carbon capture technologies specifically optimized for steel production, capturing CO2 from blast furnaces for storage or utilization.",
    maturity: "Early Commercial",
    impact: "High"
  },
  {
    id: 3,
    title: "Molten Oxide Electrolysis",
    description: "Electrochemical process that converts iron ore into metal and oxygen without carbon, powered by renewable electricity.",
    maturity: "R&D",
    impact: "Transformative"
  },
  {
    id: 4,
    title: "Advanced Scrap Sorting",
    description: "AI-powered technologies for better scrap metal classification, enabling higher recycled content in high-grade steel.",
    maturity: "Commercial",
    impact: "Medium"
  }
];

const regulatoryData = [
  {
    id: 1,
    title: "EU Carbon Border Adjustment Mechanism",
    region: "EU",
    status: "Transition Period",
    description: "Import fees on carbon-intensive products, affecting steel, cement, aluminum, fertilizer, and electricity.",
    date: "2023-2026",
    value: "Applicable"
  },
  {
    id: 2,
    title: "Corporate Sustainability Reporting Directive",
    region: "EU",
    status: "Enacted",
    description: "Expanded sustainability reporting requirements for large companies, including detailed climate disclosures.",
    date: "2024-2025"
  },
  {
    id: 3,
    title: "SEC Climate Disclosure Rule",
    region: "US",
    status: "Pending",
    description: "Standardized climate risk disclosures for public companies, including emissions data and climate-related risks.",
    date: "2023",
    value: "Applicable"
  },
  {
    id: 4,
    title: "National ETS",
    region: "China",
    status: "Active",
    description: "National emissions trading system covering power generation, with expansion to industrial sectors expected.",
    date: "2021-Present"
  },
  {
    id: 5,
    title: "Carbon Tax Implementation",
    region: "India",
    status: "Planning",
    description: "National carbon tax framework being developed to meet NDC targets, focused on high-emission industrial sectors.",
    date: "2023-2024"
  }
];

const performanceData = {
  emissionsReduction: 12.5,
  energyEfficiency: 8.3,
  renewableEnergy: 22,
  carbonIntensity: -15.2
};

const companyPerformanceData = {
  emissionsReduction: 15.8,
  energyEfficiency: 9.5,
  renewableEnergy: 27,
  carbonIntensity: -18.3
};

const peerPerformanceData = [
  {
    name: "Company A",
    emissionsReduction: 10.2,
    energyEfficiency: 7.8,
    renewableEnergy: 19,
    carbonIntensity: -12.5
  },
  {
    name: "Company B",
    emissionsReduction: 18.3,
    energyEfficiency: 11.2,
    renewableEnergy: 32,
    carbonIntensity: -20.1
  },
  {
    name: "Company C",
    emissionsReduction: 8.7,
    energyEfficiency: 5.6,
    renewableEnergy: 15,
    carbonIntensity: -9.8
  }
];

const sectorOptions = [
  { value: "cement", label: "Cement" },
  { value: "steel", label: "Steel" },
  { value: "oil-and-gas", label: "Oil & Gas" },
  { value: "chlor-alkali", label: "Chlor-Alkali" },
  { value: "textile", label: "Textile" },
  { value: "aluminium", label: "Aluminium" },
  { value: "fertilizer", label: "Fertilizer" },
  { value: "power", label: "Power" },
  { value: "pulp-and-paper", label: "Pulp & Paper" }
];

const Dashboard = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("performance");
  const [selectedSector, setSelectedSector] = useState("");
  const { toast, dismiss, toasts } = useToast();
  const { sectors, isLoading: loadingSectors } = useSectors();

  const [selectedRegion, setSelectedRegion] = useState('All');
  const regions = ['All', 'India', 'EU', 'US', 'China', 'Global'];
  
  // Add state for caching tab data
  const [cachedMaterialTopics, setCachedMaterialTopics] = useState([]);
  const [cachedClimateRisks, setCachedClimateRisks] = useState<ClimateRisk[]>([]);
  const [cachedTechnologies, setCachedTechnologies] = useState<Technology[]>([]);
  const [cachedRegulations, setCachedRegulations] = useState(regulatoryData);
  const [isLoadingTabData, setIsLoadingTabData] = useState(false);
  const [tabDataError, setTabDataError] = useState<string | null>(null);
  
  // Update selected sector when sectors are loaded
  useEffect(() => {
    if (sectors.length > 0 && !selectedSector) {
      setSelectedSector(sectors[0].name.toLowerCase().replace(/\s+/g, '-'));
    }
  }, [sectors, selectedSector]);

  // Fetch all tab data on page load
  useEffect(() => {
    if (!selectedSector) return;
    
    const sectorName = sectors.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === selectedSector)?.name || '';
    if (!sectorName) return;
    
    let isMounted = true;
    setIsLoadingTabData(true);
    setTabDataError(null);
    
    const fetchAllTabData = async () => {
      try {
        // Fetch material topics
        const materialTopicsData = await fetchMaterialTopicsData(sectorName);
        if (isMounted) {
          setCachedMaterialTopics(materialTopicsData || []);
        }
        
        // Fetch climate risks
        const risks = await fetchClimateRisks(sectorName, { toast, dismiss, toasts });
        if (isMounted && risks) {
          setCachedClimateRisks(risks);
        }
        
        // Fetch technologies
        const techs = await fetchTechnologies(sectorName, { toast, dismiss, toasts });
        if (isMounted && techs) {
          setCachedTechnologies(techs);
        }
        
        // For regulations, we're using static data for now, but in a real app this would be an API call
        // This simulates an API fetch for regulations
        if (isMounted) {
          setCachedRegulations(regulatoryData);
        }
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        if (isMounted) {
          setTabDataError("Failed to load some dashboard data");
        }
      } finally {
        if (isMounted) {
          setIsLoadingTabData(false);
        }
      }
    };
    
    fetchAllTabData();
    
    return () => {
      isMounted = false;
    };
  }, [selectedSector, sectors]);

  const handleSectorChange = (value: string) => {
    setSelectedSector(value);
    navigate(`/domain-corpus/${value}`);
    toast({
      title: "Navigating to Sector",
      description: `Viewing corpus for ${sectors.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === value)?.name || value}`,
    });
  };

  return (
    <div className={`max-w-7xl mx-auto px-4 md:px-8 py-6 md:py-10`}>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">
          Sector Intelligence Dashboard
        </h1>
        <div className="flex items-center gap-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                {loadingSectors ? 'Loading sectors...' : (sectors.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === selectedSector)?.name || 'Select Sector')}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Your Sectors</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {loadingSectors ? (
                <DropdownMenuItem disabled>Loading sectors...</DropdownMenuItem>
              ) : sectors.length === 0 ? (
                <DropdownMenuItem disabled>No sectors available</DropdownMenuItem>
              ) : (
                sectors.map((sector) => {
                  const sectorValue = sector.name.toLowerCase().replace(/\s+/g, '-');
                  return (
                    <DropdownMenuItem
                      key={sector.uuid}
                      className="flex items-center justify-between"
                      onClick={() => handleSectorChange(sectorValue)}
                    >
                      {sector.name}
                      {selectedSector === sectorValue && (
                        <Check className="h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                  );
                })
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <p className="text-muted-foreground mb-8">
        Explore climate insights and analyze trends relevant to your industry
      </p>

      <Tabs defaultValue="performance" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-5 mb-8 hidden md:grid">
          <TabsTrigger 
            value="performance" 
            className={`flex items-center gap-2 transition-all duration-200 min-w-0 ${
              activeTab === "performance" ? 
              "bg-recrea-turquoise text-white font-medium shadow-md scale-[1.02] border-b-2 border-recrea-turquoise" : 
              ""
            }`}
          >
            <BarChart size={16} className="flex-shrink-0" />
            <span className="hidden md:block truncate">Performance & Sector Updates</span>
            <span className="block md:hidden">Performance</span>
          </TabsTrigger>
          <TabsTrigger 
            value="material-topics" 
            className={`flex items-center gap-2 transition-all duration-200 min-w-0 ${
              activeTab === "material-topics" ? 
              "bg-recrea-teal text-white font-medium shadow-md scale-[1.02] border-b-2 border-recrea-teal" : 
              ""
            }`}
          >
            <Leaf size={16} className="flex-shrink-0" />
            <span className="hidden sm:block truncate">Material Topics</span>
          </TabsTrigger>
          <TabsTrigger 
            value="climate-risks" 
            className={`flex items-center gap-2 transition-all duration-200 ${
              activeTab === "climate-risks" ? 
              "bg-recrea-light-green text-white font-medium shadow-md scale-[1.02] border-b-2 border-recrea-light-green" : 
              ""
            }`}
          >
            <AlertCircle size={16} />
            <span className="hidden sm:inline">Climate Risks</span>
          </TabsTrigger>
          <TabsTrigger 
            value="technologies" 
            className={`flex items-center gap-2 transition-all duration-200 ${
              activeTab === "technologies" ? 
              "bg-recrea-green text-white font-medium shadow-md scale-[1.02] border-b-2 border-recrea-green" : 
              ""
            }`}
          >
            <Zap size={16} />
            <span className="hidden sm:inline">Technologies</span>
          </TabsTrigger>
          <TabsTrigger 
            value="regulatory" 
            className={`flex items-center gap-2 transition-all duration-200 ${
              activeTab === "regulatory" ? 
              "bg-recrea-blue text-white font-medium shadow-md scale-[1.02] border-b-2 border-recrea-blue" : 
              ""
            }`}
          >
            <FileText size={16} />
            <span className="hidden sm:inline">Regulations</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="performance" className="animate-fade-in">
          <PerformanceTab 
            peerPerformanceData={peerPerformanceData}
            performanceData={performanceData}
            selectedSector={selectedSector}
            sectors={sectors}
          />
        </TabsContent>
        
        <TabsContent value="material-topics" className="animate-fade-in">
          <MaterialTopicsTab 
            selectedSector={selectedSector}
            sectorOptions={sectorOptions}
            materialTopicsData={cachedMaterialTopics}
            isLoading={isLoadingTabData}
            error={tabDataError}
          />
        </TabsContent>
        
        <TabsContent value="climate-risks" className="animate-fade-in">
          <ClimateRisksTab 
            selectedSector={selectedSector}
            sectorOptions={sectorOptions}
            climateRisksData={cachedClimateRisks}
            isLoading={isLoadingTabData}
            error={tabDataError}
          />
        </TabsContent>
        
        <TabsContent value="technologies" className="animate-fade-in">
          <TechnologiesTab 
            selectedSector={selectedSector}
            sectorOptions={sectorOptions}
            technologiesData={cachedTechnologies}
            isLoading={isLoadingTabData}
            error={tabDataError}
          />
        </TabsContent>
        
        <TabsContent value="regulatory" className="animate-fade-in">
          <RegulatoryTab 
            regulatoryData={cachedRegulations}
            selectedRegion={selectedRegion}
            setSelectedRegion={setSelectedRegion}
            regions={regions}
            isLoading={isLoadingTabData}
            error={tabDataError}
            selectedSector={selectedSector} // Pass selectedSector to RegulatoryTab
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
