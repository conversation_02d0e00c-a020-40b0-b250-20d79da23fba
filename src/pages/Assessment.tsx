
import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/components/ThemeProvider';

const Assessment = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      
      <main className="flex-1 container max-w-5xl mx-auto px-4 py-8">
        <Button 
          variant="ghost" 
          className="mb-6 hover:bg-transparent hover:text-recrea-turquoise flex items-center gap-1"
          onClick={() => navigate('/dashboard')}
        >
          <ArrowLeft size={16} />
          Back to Dashboard
        </Button>
        
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">Climate Risk & Opportunity Assessment</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Analyze your organization's exposure to climate-related risks and identify opportunities
            for resilience and growth in a low-carbon economy.
          </p>
        </div>
        
        <Card className={`mb-10 ${theme === 'dark' ? 'border-recrea-card-border' : 'border-recrea-mint'}`}>
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Coming Soon: Digital Twin Climate Modeling</CardTitle>
            <CardDescription>
              Our advanced climate scenario modeling tool is currently in development.
              Stay tuned for the launch of our comprehensive assessment platform.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <div className="w-full max-w-md p-8 rounded-xl bg-recrea-turquoise/10 border border-recrea-turquoise/20 text-center">
              <p className="text-lg mb-4">🌍 Phase 2 Launch Expected</p>
              <p className="text-4xl font-bold text-recrea-turquoise">Q3 2025</p>
              <p className="mt-4 text-muted-foreground">
                Request early access to our beta program by contacting our team.
              </p>
            </div>
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className={`${theme === 'dark' ? 'border-recrea-card-border' : 'border-recrea-mint'}`}>
            <CardHeader>
              <CardTitle>Request a Demo</CardTitle>
              <CardDescription>
                Schedule a personalized walkthrough of our climate risk assessment platform.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full bg-recrea-turquoise text-recrea-dark hover:bg-recrea-teal">
                Book Demo Session
              </Button>
            </CardContent>
          </Card>
          
          <Card className={`${theme === 'dark' ? 'border-recrea-card-border' : 'border-recrea-mint'}`}>
            <CardHeader>
              <CardTitle>Download Sample Report</CardTitle>
              <CardDescription>
                Get a preview of our assessment outputs with a sample climate risk report.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full" variant="outline">
                Download PDF (2.4 MB)
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default Assessment;
