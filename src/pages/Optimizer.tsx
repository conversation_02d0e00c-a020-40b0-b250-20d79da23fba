import React, { useState, useEffect } from 'react';
import { useTheme } from '@/components/ThemeProvider';
import { useNavigate } from 'react-router-dom';
import { useSectors } from '@/hooks/useSectors';
import { useToast } from '@/hooks/use-toast';
import {
  FlaskConical,
  Hammer,
  Droplets,
  Atom,
  Cpu,
  BoxSelect,
  Sprout,
  Zap,
  Newspaper,
  TrendingUp,
  Leaf,
  CircuitBoard,
  AlertCircle,
  Check,
} from 'lucide-react';

// Industry boxes data
const industryData = [
  {
    id: 'cement',
    name: 'Cement',
    icon: <FlaskConical size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'steel',
    name: 'Steel',
    icon: <Hammer size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'oil-gas',
    name: 'Oil & Gas',
    icon: <Droplets size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'chlor-alkali',
    name: 'Chlor-Alkali',
    icon: <Atom size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'textile',
    name: 'Textile',
    icon: <Cpu size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'aluminium',
    name: 'Aluminium',
    icon: <BoxSelect size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'fertilizer',
    name: 'Fertilizer',
    icon: <Sprout size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'power',
    name: 'Power',
    icon: <Zap size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
  {
    id: 'pulp-paper',
    name: 'Pulp & Paper',
    icon: <Newspaper size={48} strokeWidth={1.8} className="text-recrea-teal" />,
  },
];

// New feature cards below the boxes
const featureData = [
  {
    icon: <Droplets size={48} className="text-white" />,
    bg: 'bg-gradient-to-br from-[#51C2AF] to-[#47B0DB]',
    title: "Digital Twin Customized for Your Sector",
  },
  {
    icon: <TrendingUp size={48} className="text-white" />,
    bg: 'bg-gradient-to-br from-[#51C2AF] to-[#47B0DB]',
    title: "Analysis for Base Case/ Current Scenario",
  },
  {
    icon: <Leaf size={48} className="text-white" />,
    bg: 'bg-gradient-to-br from-[#51C2AF] to-[#47B0DB]',
    title: "Analysis for Energy Transition and Decarbonization, Using BATs",
  },
  {
    icon: <CircuitBoard size={48} className="text-white" />,
    bg: 'bg-gradient-to-br from-[#51C2AF] to-[#47B0DB]',
    title: "Short and Long-term Scenario Analysis for Changes in Technology, Fuel, and Fixed & Variable Costs",
  },
];

const Optimizer = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [activeId, setActiveId] = useState<string | null>(null);
  const { sectors, isLoading: loadingSectors, error } = useSectors();
  const { toast } = useToast();
  
  // Map sector names to industry IDs
  const userSectorIds = sectors.map(sector => 
    sector.name.toLowerCase().replace(/\s+/g, '-').replace(/&/g, '-')
  );

  const handleIndustryClick = (industryId: string) => {
    // Check if sector is associated with user
    const isUserSector = userSectorIds.includes(industryId);
    
    // If it's not a user sector, show a toast and don't navigate
    if (!isUserSector) {
      toast({
        title: "Sector not available",
        description: "This sector is not associated with your account.",
        variant: "destructive",
      });
      return;
    }
    
    setActiveId(industryId);
    setTimeout(() => navigate(`/industry-flow/${industryId}`), 100);
  };

  return (
    <div
      className="max-w-7xl mx-auto px-4 md:px-8 py-6 md:py-10"
      style={{
        background: theme === 'dark'
          ? 'linear-gradient(180deg, #181b22 70%, #141926 100%)'
          : 'linear-gradient(175deg, #F0FDF8 50%, #F6FEFC 100%)'
      }}
    >
      {/* Heading */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">
          Industry Optimizer
        </h1>
      </div>
      <p className="text-muted-foreground mb-8 text-center">
        Select an industry to optimize its climate impact
      </p>
      
      {loadingSectors ? (
        <div className="flex justify-center items-center py-12">
          <div className="flex flex-col items-center gap-4">
            <div className="h-10 w-10 animate-spin rounded-full border-4 border-recrea-teal border-t-transparent"></div>
            <p className="text-muted-foreground">Loading your sectors...</p>
          </div>
        </div>
      ) : error ? (
        <div className="flex justify-center items-center py-12">
          <div className="flex flex-col items-center gap-4">
            <AlertCircle className="h-12 w-12 text-destructive" />
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      ) : (
        <div
          className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 justify-items-center"
        >
          {industryData.map((industry) => {
            const isActive = activeId === industry.id;
            const isUserSector = userSectorIds.includes(industry.id);
            
            return (
              <button
                key={industry.id}
                className={`
                  flex flex-col items-center justify-center w-36 h-36 md:w-40 md:h-40 
                  ${isUserSector ? 'bg-white dark:bg-recrea-card-bg/80' : 'bg-gray-100 dark:bg-gray-800/50'}
                  rounded-2xl shadow-sm border
                  ${isUserSector ? 'border-transparent' : 'border-gray-200 dark:border-gray-700'}
                  transition-all duration-150
                  ${isUserSector ? 'cursor-pointer' : 'cursor-not-allowed opacity-70'}
                  outline-none focus-visible:ring-2
                  ${
                    isActive
                      ? 'border-recrea-teal ring-2 ring-recrea-teal'
                      : isUserSector ? 'hover:shadow-lg hover:border-recrea-mint' : ''
                  }
                  ${theme === 'dark' ? (isUserSector ? 'hover:bg-recrea-card-bg/60' : '') : (isUserSector ? 'hover:bg-recrea-light-mint' : '')}
                `}
                style={isUserSector ? { boxShadow: '0 3px 24px 0 rgba(80, 201, 150, 0.06)' } : {}}
                onClick={() => handleIndustryClick(industry.id)}
                onMouseDown={() => isUserSector && setActiveId(industry.id)}
                onMouseUp={() => isUserSector && setActiveId(null)}
                onBlur={() => isUserSector && setActiveId(null)}
                disabled={!isUserSector}
                tabIndex={isUserSector ? 0 : -1}
              >
                <span
                  className={`
                    mb-4 transition-colors duration-150
                    ${isActive ? 'text-recrea-teal' : ''}
                    ${!isUserSector ? 'text-gray-400 dark:text-gray-600' : ''}
                  `}
                >
                  {industry.icon}
                </span>
                <span className={`font-medium text-base md:text-lg ${
                  isActive ? 'text-recrea-teal' : 
                  isUserSector ? 'text-recrea-dark dark:text-white' : 
                  'text-gray-500 dark:text-gray-400'
                }`}>
                  {industry.name}
                </span>
                {isUserSector && (
                  <span className="absolute top-2 right-2">
                    <Check size={16} className="text-recrea-teal" />
                  </span>
                )}
                {isActive && (
                  <span className="absolute bottom-4 right-4 bg-white dark:bg-recrea-card-bg rounded-full shadow p-1 border border-recrea-teal">
                    <svg width="28" height="28" fill="none" viewBox="0 0 28 28"><circle cx="14" cy="14" r="13" stroke="#50C996" strokeWidth="2" fill="#fff"/><path d="M11 8l6 6-6 6" stroke="#50C996" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  </span>
                )}
              </button>
            );
          })}
        </div>
      )}

      {/* New: Informational Section Below */}
      <div className="mt-20 mb-2 w-full">
        <h2 className="text-2xl md:text-3xl font-bold text-center text-[#29405B] mb-5">
          Energy Transition and Decarbonization Powered by AI
        </h2>
        <p className="text-center text-lg md:text-xl mb-8 text-[#2B415B]/75 max-w-3xl mx-auto">
          Through a combination of our AI model and a rich database of Best Available Technologies (BAT), we generate both short and long-term, cost-optimal investment plans for your decarbonization and emission reduction targets.
        </p>
        <div className="w-full bg-[#E7F5F7] py-12 rounded-2xl">
          <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 px-4">
            {featureData.map((feature, idx) => (
              <div
                key={idx}
                className="bg-white rounded-2xl p-8 flex flex-col items-center shadow-lg transition-all hover:shadow-xl"
                style={{
                  minHeight: 240,
                  boxShadow: '0 1px 14px 0 rgba(37, 136, 170, 0.10)'
                }}
              >
                <div className={`mb-6 flex items-center justify-center rounded-full ${feature.bg}`} style={{ width: 70, height: 70 }}>
                  {feature.icon}
                </div>
                <div className="text-[#29405B] font-medium text-center text-base md:text-lg leading-6">
                  {feature.title}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Optimizer;
