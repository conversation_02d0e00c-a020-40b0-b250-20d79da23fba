
import { useNavigate } from 'react-router-dom';
import InsightCard from '@/components/InsightCard';
import SearchInterface from '@/components/SearchInterface';
import { Button } from '@/components/ui/button';
import { useTheme } from '@/components/ThemeProvider';

const insightData = [
  {
    title: "Green Hydrogen in Steel",
    description: "Steel manufacturers are exploring green hydrogen as a clean alternative to coal in the iron ore reduction process, potentially cutting CO2 emissions significantly.",
    modalContent: "Steel manufacturers are exploring green hydrogen as a clean alternative to coal in the iron ore reduction process, potentially cutting CO2 emissions significantly. The transition to hydrogen-based steelmaking could reduce the carbon footprint of steel production by up to 80%, making it a critical pathway for decarbonizing one of the world's most carbon-intensive industries. Early pilot plants in Sweden and Germany have shown promising results, with commercial-scale plants expected within the decade."
  },
  {
    title: "Electric Arc Furnaces",
    description: "The shift towards electric arc furnaces for steel production, especially when powered by renewable energy, is reducing the industry's carbon footprint significantly.",
    modalContent: "The shift towards electric arc furnaces for steel production, especially when powered by renewable energy, is reducing the industry's carbon footprint significantly. Electric arc furnaces (EAFs) can reduce carbon emissions by 75% compared to traditional blast furnaces when powered by renewable electricity. They also offer greater flexibility in production and can more efficiently process recycled steel scrap, creating a more circular steel economy."
  },
  {
    title: "Carbon Capture in Steel",
    description: "Implementing carbon capture and storage (CCS) technologies in steel plants can help mitigate emissions from traditional blast furnace operations.",
    modalContent: "Implementing carbon capture and storage (CCS) technologies in steel plants can help mitigate emissions from traditional blast furnace operations. CCS technologies can capture up to 90% of CO2 emissions from steel production, though the challenge remains in scaling these solutions cost-effectively. Several major steel producers are piloting CCS projects to evaluate their potential for industrial-scale implementation."
  },
  {
    title: "Circular Economy",
    description: "Increasing the use of scrap steel in production through improved recycling systems is lowering the overall energy demand and emissions.",
    modalContent: "Increasing the use of scrap steel in production through improved recycling systems is lowering the overall energy demand and emissions. Recycling steel requires only about 30% of the energy needed to produce primary steel from iron ore. Advances in scrap sorting technologies are improving the quality of recycled steel, making it viable for more high-value applications and reducing dependency on primary steel production."
  }
];

const Index = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  
  return (
    <div className={`min-h-screen flex flex-col ${theme === 'dark' ? 'bg-recrea-dark text-white' : 'bg-white text-recrea-dark'}`}>
      <main className="flex-1 w-full max-w-7xl mx-auto px-4 md:px-8 py-10">
        {/* Hero Section */}
        <div className="hero-section py-16 md:py-24 mb-16 text-center animate-fade-in">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className={theme === 'dark' ? 'text-white' : 'text-recrea-dark'}>Climate Solutions with </span>
            <span className="text-recrea-green">AI</span>
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto mb-10 text-muted-foreground">
            Leveraging artificial intelligence to accelerate sustainable solutions 
            and drive decarbonization across industries.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              className="primary-button text-lg py-6 px-8"
              onClick={() => navigate('/signup')}
            >
              Get Started
            </Button>
            <Button 
              variant="outline"
              className={`text-lg py-6 px-8 border-2 ${theme === 'dark' ? 'border-recrea-card-border text-white' : 'border-gray-300 text-recrea-dark'} hover:border-recrea-green hover:text-recrea-green`}
              onClick={() => navigate('/domain-corpus')}
            >
              Explore Domain Corpus
            </Button>
          </div>
        </div>

        <div className="text-center mb-8 animate-fade-in">
          <p className="text-muted-foreground mb-6">Recreating a better tomorrow</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-12">
            Key Insights: <span className="text-recrea-green">Decarbonization in Steel Industry</span>
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
          {insightData.map((insight, index) => (
            <InsightCard 
              key={index}
              title={insight.title} 
              description={insight.description}
              modalContent={insight.modalContent}
              className={`animate-fade-in`}
              style={{ animationDelay: `${index * 100}ms` }}
            />
          ))}
        </div>
        
        <SearchInterface />
      </main>
    </div>
  );
};

export default Index;

