import React, { useMemo, useRef, useState, useEffect } from 'react';
import { useParams, Link, useLocation } from 'react-router-dom';
import { ArrowLeft, ChevronDown, Check } from 'lucide-react';
import { ConnectionDetailsModal } from '@/components/ConnectionDetailsModal';
import { useRegions } from '@/hooks/useRegions';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { useToastContext } from '@/contexts/ToastContext';
import { SupplierTechDialog } from '@/components/SupplierTechDialog';
import { fetchTechnologyByName, fetchSuppliersByTechnology } from '@/api/techDetailsApi';
import { Skeleton } from '@/components/ui/skeleton';

const TechDetails = () => {
  const location = useLocation();
  const { toast } = useToastContext();
  const reactFlowRef = useRef<any>(null);
  const { activityId, techSlug } = useParams();
  const urlParams = new URLSearchParams(location.search);
  const sector = urlParams.get('sector') || 'steel';
  const [dialogState, setDialogState] = useState({
    isConnectionModalOpen: false,
    selectedSupplier: null as any,
    showConnectionDialog: false,
  });
  const [selectedRegion, setSelectedRegion] = useState('global');

  const [technologyState, setTechnologyState] = useState({
    data: null as any,
    loading: true,
    error: null as string | null,
  });

  const [suppliersState, setSuppliersState] = useState({
    data: [] as any[],
    loading: false,
    error: null as string | null,
  });

  useEffect(() => {
    const fetchTechDetails = async () => {
      setTechnologyState(prev => ({ ...prev, loading: true, error: null }));
      try {
        if (!techSlug) throw new Error('No technology specified');
        const data = await fetchTechnologyByName(techSlug);
        // const data = await fetchTechnologyByName("Boiler");
        setTechnologyState(prev => ({ ...prev, data: data, loading: false }));
      } catch (err: any) {
        setTechnologyState(prev => ({ ...prev, error: err.message || 'Failed to fetch technology details', data: null, loading: false }));
      }
    };
    fetchTechDetails();
  }, [techSlug]);

  useEffect(() => {
    const fetchSuppliers = async () => {
      setSuppliersState(prev => ({ ...prev, loading: true, error: null }));
      try {
        if (!techSlug) throw new Error('No technology specified');
        const data = await fetchSuppliersByTechnology(techSlug);
        // const data = await fetchSuppliersByTechnology("Boiler");
        setSuppliersState(prev => ({ ...prev, data: data, loading: false, }));
      } catch (err: any) {
        setSuppliersState(prev => ({ ...prev, error: err.message || 'Failed to fetch suppliers', data: [], loading: false }));
      }
    };
    fetchSuppliers();
  }, [techSlug]);

  const { regions, loadingRegions, regionsError } = useRegions(true);

  // Filter suppliers by selected region
  const filteredSuppliers = selectedRegion === 'global'
    ? suppliersState.data
    : suppliersState.data.filter((s: any) => {
        if (!s.region) return false;
        // s.region can be an object with code or name
        if (typeof s.region === 'object' && s.region.code) {
          return s.region.code.toLowerCase() === selectedRegion.toLowerCase();
        }
        if (typeof s.region === 'string') {
          return s.region.toLowerCase() === selectedRegion.toLowerCase();
        }
        return false;
      });

  console.log("filteredSuppliers", filteredSuppliers);
  const handleViewDetails = (supplier: any) => {
    setDialogState(prev => ({ ...prev, selectedSupplier: supplier, showConnectionDialog: true }));
  };

  const handleConnectionModalClose = () => {
    setDialogState(prev => ({ ...prev, isConnectionModalOpen: false, selectedSupplier: null, showConnectionDialog: false }));
  };

  const handleConnectionModalNext = (data: any) => {
    console.log('Connection details data:', data);
    // Handle the connection details data here
    handleConnectionModalClose();
  };

  const handleAddTechnology = () => {
    console.log('Add technology clicked');
    // Handle add technology action here
  };

  const handleRegionChange = (regionCode: string) => {
    setSelectedRegion(regionCode);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 md:px-8 py-8">
      <Link to={`/marketplace/${activityId}?sector=${sector}`} className="text-muted-foreground text-sm flex items-center mb-6 gap-2 no-underline hover:no-underline focus:no-underline">
        <ArrowLeft className="h-4 w-4" /> Back to Activity
      </Link>

      {(!technologyState.loading && (technologyState.error || !technologyState.data)) ?
         <div className="p-8 text-red-500">{technologyState.error || "Technology not found."}</div>
      :
        <div className="bg-white rounded-xl border p-6 md:p-8 shadow-sm">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div>
            <h1 className="text-2xl font-bold mb-2">{technologyState.data?.name}</h1>
              <div className="text-muted-foreground mb-4">{technologyState.data?.subtext}</div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex items-center gap-2">
                  {loadingRegions
                    ? 'Loading regions...'
                    : (regions.find(r => r.code === selectedRegion)?.name || 'Select Region')}
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Region</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {loadingRegions ? (
                  <DropdownMenuItem disabled>Loading regions...</DropdownMenuItem>
                ) : regionsError ? (
                  <DropdownMenuItem disabled>{regionsError}</DropdownMenuItem>
                ) : regions.length === 0 ? (
                  <DropdownMenuItem disabled>No regions available</DropdownMenuItem>
                ) : (
                  regions.map(region => (
                    <DropdownMenuItem
                      key={region.code}
                      className="flex items-center justify-between"
                      onClick={() => handleRegionChange(region.code)}
                    >
                      {region.name}
                      {selectedRegion === region.code && (
                        <Check className="h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                  ))
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="text-gray-700 mt-4 mb-4">
            { technologyState.loading ?
              <div className="h-6 w-1/3 bg-muted animate-pulse rounded mb-4" />
            : technologyState.data?.description ? 
              technologyState.data.description 
            : 
              <span className="text-gray-400">No description available.</span>
            }</div>
          {/* {technology.technologyProcessSteps && technology.technologyProcessSteps.length > 0 && (
            <div className="mb-4">
              <div className="font-semibold text-xs mb-2">Process Steps:</div>
              <div className="flex flex-wrap gap-2">
                {technology.technologyProcessSteps.map((step, i) => (
                  <span key={i} className="inline-block bg-recrea-light-mint text-green-700 rounded-full px-3 py-1 text-xs font-medium border border-green-200">{step}</span>
                ))}
              </div>
            </div>
          )} */}
          {technologyState.loading && suppliersState.loading ? (
            <div className="mt-4">
              <div className="font-semibold text-xs mb-2">Suppliers:</div>
              <div className="space-y-4">
                {[...Array(4)].map((_, idx) => (
                  <div key={idx} className="flex items-center bg-gray-50 p-4 rounded-lg shadow-sm">
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-2">
                        <div>
                          <Skeleton className="h-5 w-32 mb-1 rounded" />
                          <Skeleton className="h-3 w-20 mb-1 rounded" />
                        </div>
                        <Skeleton className="h-7 w-20 rounded" />
                      </div>
                      <Skeleton className="h-3 w-3/4 mb-1 rounded" />
                      <Skeleton className="h-3 w-1/2 mb-1 rounded" />
                      <div className="flex gap-1 mt-1">
                        <Skeleton className="h-5 w-16 rounded" />
                        <Skeleton className="h-5 w-12 rounded" />
                      </div>
                      <div className="mt-2 space-y-1">
                        <Skeleton className="h-3 w-24 rounded" />
                        <Skeleton className="h-3 w-20 rounded" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : suppliersState.error ? (
            <div className="mt-4 text-red-500 text-sm">{suppliersState.error}</div>
          ) : filteredSuppliers?.length > 0 ? (
            <div className="mt-4">
              <div className="font-semibold text-xs mb-2">Suppliers:</div>
              <div className="space-y-4">
                {filteredSuppliers?.map((s, i) => (
                  <div key={i} className="flex items-center bg-gray-50 p-4 rounded-lg shadow-sm hover:shadow-md transition">
                    {/* {s.logo && (
                      <img src={s.logo} alt={s.company_name || 'Supplier Logo'} className="w-14 h-14 rounded-full mr-4 object-cover border border-gray-200" />
                    )} */}
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-2">
                        <div>
                          <div className="text-lg font-bold text-green-800">{s.company_name || 'N/A'}</div>
                          {s.supplier_type && <div className="text-xs text-gray-500 mb-1">{s.supplier_type}</div>}
                        </div>
                        <button
                          className="bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold hover:bg-blue-700 transition"
                          onClick={() => handleViewDetails(s)}
                        >
                          View Details
                        </button>
                      </div>
                      {s.description && <div className="text-xs text-gray-600 mb-1">{s.description}</div>}
                      {s.region && <div className="text-xs text-gray-400 mb-1">{s.region.name}</div>}
                      {s.specialties && s.specialties.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {s.specialties.map((tag: string) => (
                            <span key={tag} className="bg-green-100 text-green-700 px-2 py-0.5 rounded text-xs">{tag}</span>
                          ))}
                        </div>
                      )}
                      {(s.contact_details?.email || s.contact_details?.phone || s.contact_details?.website || s.address) && (
                        <div className="mt-2 text-xs text-gray-700 space-y-1">
                          {s.contact_details?.email && (
                            <div>Email: <a href={`mailto:${s.contact_details.email}`} className="text-blue-600 underline">{s.contact_details.email}</a></div>
                          )}
                          {s.contact_details?.phone && (
                            <div>Phone: <a href={`tel:${s.contact_details.phone}`} className="text-blue-600 underline">{s.contact_details.phone}</a></div>
                          )}
                          {s.contact_details?.website && (
                            <div>Website: <a href={s.contact_details.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">{s.contact_details.website}</a></div>
                          )}
                          {s.address && (
                            <div>Address: {s.address}</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="mt-4 text-gray-500 text-sm">No suppliers found for the selected region.</div>
          )}
        </div>
      }
      {/* Connection Details Modal */}
      <ConnectionDetailsModal
        open={dialogState.isConnectionModalOpen}
        onClose={handleConnectionModalClose}
        activityName={technologyState.data?.title}
        onNext={handleConnectionModalNext}
        onAddTechnology={handleAddTechnology}
      />

      <SupplierTechDialog
        open={dialogState.showConnectionDialog}
        onClose={handleConnectionModalClose}
        onComplete={handleConnectionModalNext}
        supplierCompanyName={dialogState.selectedSupplier?.company_name || ''}
      />
    </div>
  );
};

export default TechDetails;
