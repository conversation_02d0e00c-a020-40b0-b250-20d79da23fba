import { MarkerType } from "@xyflow/react";

// Horizontal spacing between nodes - increased for better readability
const HORIZONTAL_GAP = 350;
// Vertical spacing between rows - increased for better readability
const ROW_GAP = 250;
// Starting X position
const START_X = 150;
// Starting Y position for first row
const ROW1_Y = 120;
// Starting Y position for second row
const ROW2_Y = ROW1_Y + ROW_GAP;
// Starting Y position for third row (final output)
const ROW3_Y = ROW2_Y + ROW_GAP;

export const initialNodes = [
  // First Row - First node: Gas Oil Separation
  {
    id: '1',
    data: { label: 'Gas Oil Separation' },
    position: { x: START_X, y: ROW1_Y },
    style: {},
    type: 'custom',
  },
  // First Row - Second node: Condensation
  {
    id: '2',
    data: { label: 'Condensation' },
    position: { x: START_X + HORIZONTAL_GAP, y: ROW1_Y },
    style: {},
    type: 'custom',
  },
  // First Row - Third node: Amine Absorbing
  {
    id: '3',
    data: { label: 'Amine Absorbing' },
    position: { x: START_X + (HORIZONTAL_GAP * 2), y: ROW1_Y },
    style: {},
    type: 'custom',
  },
  // First Row - Fourth node: Water Saturation
  {
    id: '4',
    data: { label: 'Water Saturation' },
    position: { x: START_X + (HORIZONTAL_GAP * 3), y: ROW1_Y },
    style: {},
    type: 'custom',
  },
  // Second Row - Fifth node: Glycol Contacting (now rightmost in row 2)
  {
    id: '5',
    data: { label: 'Glycol Contacting' },
    position: { x: START_X + (HORIZONTAL_GAP * 3), y: ROW2_Y },
    style: {},
    type: 'custom',
  },
  // Second Row - Sixth node: Mercury Removal (second from right)
  {
    id: '6',
    data: { label: 'Mercury Removal' },
    position: { x: START_X + (HORIZONTAL_GAP * 2), y: ROW2_Y },
    style: {},
    type: 'custom',
  },
  // Second Row - Seventh node: Mercury Gas Condensation (third from right)
  {
    id: '7',
    data: { label: 'Mercury Gas Condensation' },
    position: { x: START_X + HORIZONTAL_GAP, y: ROW2_Y },
    style: {},
    type: 'custom',
  },
  // Second Row - Eighth node: Cryogenic Seperation (leftmost in row 2)
  {
    id: '8',
    data: { label: 'Cryogenic Seperation' },
    position: { x: START_X, y: ROW2_Y },
    style: {},
    type: 'custom',
  },
  // Note: Final output nodes are now created dynamically based on form data
];

// Input nodes removed - inputs will be handled as standalone edges
export const inputNodes = [];

export const edgeLabelStyle = {
  fill: '#787882',
  fontWeight: 400,
  fontSize: 13.5,
  userSelect: "none",
  background: "rgba(255,255,255,0.7)",
  padding: "2px 5px",
  borderRadius: "3px",
  border: "1px solid #ded3fd",
};

export const edgeStyle = {
  stroke: '#9b87f5',
  strokeWidth: 2,
  opacity: 0.85,
};

// Add a virtual input node for the crude oil edge
export const virtualInputNodes = [
  {
    id: 'virtual-input-crude',
    data: { label: 'Crude Oil' },
    position: { x: START_X - 200, y: ROW1_Y },
    style: {
      width: 1,
      height: 1,
      opacity: 0,
      pointerEvents: 'none',
    },
    type: 'input',
  },
];

export const initialEdges = [
  // Input edge: Crude oil to Gas Oil Separation
  {
    id: 'input-crude-1',
    source: 'virtual-input-crude',
    target: '1',
    targetHandle: 'left-target',
    label: 'Crude oil',
    style: {
      stroke: "#10B981", // Green color for input edges
      strokeWidth: 3,
      opacity: 0.9,
    },
    markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
    labelStyle: edgeLabelStyle,
    type: 'smoothstep',
    data: {
      type: 'input-material',
      material: 'Crude oil',
      sourceActivity: 'Nil',
      technology: 'Nil',
      isInputEdge: true
    }
  },
  // First Row Connections (Unchanged - Left to Right)
  // Connect first node to second node
  { 
    id: 'e1-2', 
    source: '1', 
    sourceHandle: 'right-source',
    target: '2', 
    targetHandle: 'left-target',
    label: 'Raw gas',
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Connect second node to third node
  { 
    id: 'e2-3', 
    source: '2', 
    sourceHandle: 'right-source',
    target: '3', 
    targetHandle: 'left-target',
    label: 'Sour gas', 
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Connect third node to fourth node
  { 
    id: 'e3-4', 
    source: '3', 
    sourceHandle: 'right-source',
    target: '4', 
    targetHandle: 'left-target',
    label: 'Sweet gas', 
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Connect fourth node to fifth node (connection between rows)
  { 
    id: 'e4-5', 
    source: '4', 
    sourceHandle: 'bottom-source',
    target: '5', 
    targetHandle: 'top-target',
    label: 'Saturated gas', 
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Second Row Connections (Modified - Right to Left)
  // Connect fifth node (right) to sixth node (left)
  { 
    id: 'e5-6', 
    source: '5', 
    sourceHandle: 'left-source',
    target: '6', 
    targetHandle: 'right-target',
    label: 'Dry gas', 
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Connect sixth node to seventh node
  { 
    id: 'e6-7', 
    source: '6', 
    sourceHandle: 'left-source',
    target: '7', 
    targetHandle: 'right-target',
    label: 'Purified gas', 
    style: edgeStyle, 
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }, 
    labelStyle: edgeLabelStyle, 
    type: 'smoothstep' 
  },
  // Connect seventh node to eighth node
  {
    id: 'e7-8',
    source: '7',
    sourceHandle: 'left-source',
    target: '8',
    targetHandle: 'right-target',
    label: 'Processed gas',
    style: edgeStyle,
    markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
    labelStyle: edgeLabelStyle,
    type: 'smoothstep'
  },
  // Note: Final output edges are now created dynamically based on form data
];

export const flowTypes = [
  { id: 'main', label: 'Main flow', color: 'bg-[#C7C2E4] text-[#684dc4]' },
  { id: 'sub', label: 'Sub flow', color: 'bg-[#e0ffe0] text-[#18cd22]' },
  { id: 'byproduct', label: 'By-product flow', color: 'bg-[#fcfdb8] text-[#e0be0b]' },
];

export const nodeDefaultStyle = {
  borderRadius: '14px',
  background: '#D6BCFA',
  border: '2px solid #9b87f5',
  color: '#222',
  minWidth: 180,
  minHeight: 60,
  fontWeight: 500,
  padding: '16px 26px',
  fontSize: 16,
  boxShadow: '0 4px 24px 0 rgba(126,105,171,0.10)',
};

export const VERTICAL_GAP = 60 + 70;
