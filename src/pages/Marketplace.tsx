import React, { useContext, useState, useEffect } from 'react';
import { useSectors } from '@/hooks/useSectors';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuItem, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Check, ChevronDown, ArrowRight, Linkedin, Star, User } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { FlaskConical, FlaskRound, Droplets, Cloud } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import marketplaceData from '@/data/marketplaceData';
import experts from '@/data/expertiseData';
import { useRegions } from '@/hooks/useRegions';
import ExpertiseSection from '@/components/dashboard/ExpertiseSection';
import { SectorActivitiesContext } from '@/contexts/SectorActivitiesContext';
import { Skeleton } from '@/components/ui/skeleton';

const Marketplace = () => {
  const {
    sectors,
    loadingSectors,
    selectedSector,
    setSelectedSector,
    activities,
    loadingActivities,
    activitiesError,
  } = useContext(SectorActivitiesContext);
  const [selectedRegion, setSelectedRegion] = useState<string>('global');
  const navigate = useNavigate();
  const location = useLocation();
  const { regions, loadingRegions, regionsError, fetchRegionsNow } = useRegions(false);

  // Parse URL parameters
  const urlParams = new URLSearchParams(location.search);
  const expertiseParam = urlParams.get('expertise');
  const regionParam = urlParams.get('region') || 'global';

  React.useEffect(() => {
    if (regionParam) {
      setSelectedRegion(regionParam);
    }
  }, [regionParam]);

  const handleSectorChange = (value: string) => {
    setSelectedSector(value);
    // Optionally update the URL or trigger filtering here
  };

  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);
    // Optionally update the URL or trigger filtering here
  };

  // Filter experts based on expertise parameter
  const filteredExperts = (expertiseParam && expertiseParam.toLowerCase() !== "regulations")
    ? experts.filter(expert => 
        expert.expertise.some(exp => 
          exp.toLowerCase().replace(/\s+/g, '-') === expertiseParam.toLowerCase()
        ) && 
        (selectedRegion === 'global' || (expert.region && expert.region.code.toLowerCase() === selectedRegion))
      )
    : experts.filter(expert => 
        selectedRegion === 'global' || (expert.region && expert.region.code.toLowerCase() === selectedRegion)
      );

  // Determine which section to show
  const showExpertise = expertiseParam !== null;
  const showActivities = !showExpertise;

  // Get activities for the selected sector
  const sectorActivities = marketplaceData[selectedSector] || [];

  // Only fetch regions if expertise section is shown and regions are not loaded
  useEffect(() => {
    if (showExpertise && regions.length === 0) {
      fetchRegionsNow();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showExpertise]);

  return (
    <div className="max-w-7xl mx-auto px-4 md:px-8 py-6 md:py-10">
      <div className="flex justify-between items-center mb-2">
        <h1 className="text-3xl font-bold">
          {showExpertise ? `Expertise` : 'Marketplace'}
        </h1>
        <div className="flex items-center gap-4">
        {showExpertise ?
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                {loadingRegions
                  ? 'Loading regions...'
                  : (regions.find(r => r.code === selectedRegion)?.name || 'Select Region')}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Region</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {loadingRegions ? (
                <DropdownMenuItem disabled>Loading regions...</DropdownMenuItem>
              ) : regionsError ? (
                <DropdownMenuItem disabled>{regionsError}</DropdownMenuItem>
              ) : regions.length === 0 ? (
                <DropdownMenuItem disabled>No regions available</DropdownMenuItem>
              ) : (
                regions.map(region => (
                  <DropdownMenuItem
                    key={region.code}
                    className="flex items-center justify-between"
                    onClick={() => handleRegionChange(region.code)}
                  >
                    {region.name}
                    {selectedRegion === region.code && (
                      <Check className="h-4 w-4" />
                    )}
                  </DropdownMenuItem>
                ))
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          : null}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                {loadingSectors ? 'Loading sectors...' : (sectors?.find(s => s.name.toLowerCase().replace(/\s+/g, '-') === selectedSector)?.name || 'Select Sector')}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Your Sectors</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {loadingSectors ? (
                <DropdownMenuItem disabled>Loading sectors...</DropdownMenuItem>
              ) : sectors?.length === 0 ? (
                <DropdownMenuItem disabled>No sectors available</DropdownMenuItem>
              ) : (
                sectors?.map((sector) => {
                  const sectorValue = sector.name.toLowerCase().replace(/\s+/g, '-');
                  return (
                    <DropdownMenuItem
                      key={sector.uuid}
                      className="flex items-center justify-between"
                      onClick={() => handleSectorChange(sectorValue)}
                    >
                      {sector.name}
                      {selectedSector === sectorValue && (
                        <Check className="h-4 w-4" />
                      )}
                    </DropdownMenuItem>
                  );
                })
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <p className="text-muted-foreground mb-8">
        {showExpertise 
          ? `Connect with our network of industry experts`
          : 'Discover cutting-edge technologies and connect with industry experts to accelerate your transition journey.'
        }
      </p>

      {/* Activities Section - Only show when no expertise parameter */}
      {showActivities && (
        <section className="mt-12 bg-recrea-light-mint rounded-xl p-8">
          <h2 className="text-2xl font-bold mb-2">Activities</h2>
          <p className="text-muted-foreground mb-8">Explore range of sustainable technologies</p>
          {loadingSectors || loadingActivities ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, idx) => (
                <Skeleton key={idx} className="h-[260px] w-full rounded-xl" />
              ))}
            </div>
          ) : activitiesError ? (
            <div className="text-center text-red-500 py-8">{activitiesError}</div>
          ) : activities?.length === 0 ? (
            <div className="text-center py-8">No activities found for this sector.</div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {activities?.map((item, idx) => (
                <Card
                  key={item.uuid}
                  className="hover:shadow-md transition-shadow cursor-pointer p-4"
                  onClick={() => navigate(`/marketplace/${item.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}?sector=${selectedSector}`)}
                  tabIndex={0}
                  role="button"
                  aria-label={`View details for ${item.name}`}
                  onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') navigate(`/marketplace/${item.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}?sector=${selectedSector}`); }}
                >
                  <div className="flex flex-row items-start gap-2 mb-2">
                    <span className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-recrea-light-mint text-2xl">{item.icon || <FlaskConical />}</span>
                  </div>
                  <CardContent className="pt-0 px-0 pb-0">
                    <CardTitle className="text-xl font-semibold mb-2">{item.name}</CardTitle>
                    <p className="text-xs text-muted-foreground text-justify leading-[1.4rem] line-clamp-3 min-h-[4.2rem] mb-3" style={{display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical', overflow: 'hidden'}}>
                      {item.subtext || 'No description available.'}
                    </p>
                    <button
                      type="button"
                      className="text-green-600 font-semibold flex items-center gap-1 hover:underline text-[0.95rem] focus:outline-none"
                      onClick={e => { e.stopPropagation(); navigate(`/marketplace/${item.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')}?sector=${selectedSector}`); }}
                      tabIndex={-1}
                    >
                      Explore Technologies <ArrowRight className="h-4 w-4" />
                    </button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </section>
      )}

      {/* Expertise Section - Only show when expertise parameter is present */}
      {showExpertise && (
        <ExpertiseSection experts={filteredExperts} />
      )}
    </div>
  );
};

export default Marketplace; 