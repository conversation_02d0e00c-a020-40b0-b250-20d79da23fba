import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '@/components/ThemeProvider';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { 
  Search as SearchIcon, 
  MessageSquare,
  ChevronRight
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { 
  SidebarProvider, 
  Sidebar, 
  SidebarContent, 
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton,
  SidebarTrigger,
  SidebarHeader,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent
} from "@/components/ui/sidebar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from '@/hooks/use-toast';

const sectorOptions = [
  { value: "steel", label: "Steel & Heavy Manufacturing" },
  { value: "energy", label: "Renewable Energy" },
  { value: "agriculture", label: "Agriculture & Food" },
  { value: "mining", label: "Mining & Minerals" },
  { value: "chemicals", label: "Chemicals & Materials" },
  { value: "construction", label: "Construction" },
  { value: "transport", label: "Transportation & Logistics" },
  { value: "retail", label: "Retail & Consumer Goods" }
];

const processSteps = [
  {
    id: 1,
    title: "Physical Risk Analysis",
    description: "Assess vulnerability to extreme weather events, resource scarcity, and infrastructure risks",
    status: "complete"
  },
  {
    id: 2,
    title: "Transition Risk Analysis",
    description: "Evaluate market, policy, legal and technology risks from the transition to a low-carbon economy",
    status: "active"
  },
  {
    id: 3,
    title: "Opportunity Assessment",
    description: "Identify resource efficiency, green products, markets, and resilience opportunities",
    status: "pending"
  },
  {
    id: 4,
    title: "Scenario Analysis",
    description: "Test business resilience against multiple climate scenarios",
    status: "pending"
  },
  {
    id: 5,
    title: "Response Strategy",
    description: "Develop mitigation, adaptation and opportunity capture strategies",
    status: "pending"
  }
];

const ClimateRiskAssessment = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [selectedSector, setSelectedSector] = useState("steel");
  const { toast } = useToast();

  const handleSectorChange = (value: string) => {
    setSelectedSector(value);
    toast({
      title: "Sector Changed",
      description: `Climate risk assessment updated for ${sectorOptions.find(s => s.value === value)?.label || value}`,
    });
  };

  const navigateToDocumentChat = () => {
    navigate('/document-chat');
  };

  const navigateToQuerySearch = () => {
    navigate('/query-search');
  };

  const navigateToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className={`min-h-screen flex w-full ${theme === 'dark' ? 'bg-recrea-dark text-white' : 'bg-white text-recrea-dark'}`}>
      <SidebarProvider>
        <Sidebar>
          <SidebarHeader>
            <div className="flex items-center px-2">
              <span className="font-semibold text-lg">Climate Intel</span>
            </div>
          </SidebarHeader>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupLabel>Query</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu>
                  <SidebarMenuItem>
                    <SidebarMenuButton 
                      tooltip="Search"
                      onClick={navigateToQuerySearch}
                    >
                      <SearchIcon className="mr-2" size={18} />
                      <span>Search</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuButton 
                      tooltip="Document Chat"
                      onClick={navigateToDocumentChat}
                    >
                      <MessageSquare className="mr-2" size={18} />
                      <span>Document Chat</span>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
          <SidebarFooter>
            <div className="px-2 py-2">
              <Select value={selectedSector} onValueChange={handleSectorChange}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Sector" />
                </SelectTrigger>
                <SelectContent>
                  {sectorOptions.map((sector) => (
                    <SelectItem key={sector.value} value={sector.value}>
                      {sector.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </SidebarFooter>
        </Sidebar>

        <div className="flex flex-col w-full">
          <Header />
          
          <div className="flex w-full">
            <div className="flex md:hidden items-center m-4">
              <SidebarTrigger />
            </div>
          </div>
          
          <main className="flex-1 w-full">
            <div className="max-w-7xl mx-auto px-4 md:px-8 py-6 md:py-10">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">
                  Assess Climate Risks and Opportunities
                </h1>
              </div>
              <p className="text-muted-foreground mb-8">
                Systematic assessment of climate-related risks and opportunities for {sectorOptions.find(s => s.value === selectedSector)?.label || selectedSector}
              </p>
              
              <div className="glass-card p-8">
                <h2 className="text-2xl font-semibold mb-6">Assessment Process Flow</h2>
                <p className="text-muted-foreground mb-8">
                  Climate risk and opportunity assessment follows a structured process to ensure comprehensive analysis and strategic decision-making.
                </p>
                
                {/* Process flow diagram */}
                <div className="overflow-x-auto pb-4">
                  <div className="flex flex-col md:flex-row gap-4 md:gap-2 min-w-[800px]">
                    {processSteps.map((step, index) => (
                      <div key={step.id} className="flex flex-1 items-center">
                        <Card className={`flex-1 border-l-4 hover:shadow-md transition-shadow ${
                          step.status === 'complete' ? 'border-l-green-500' :
                          step.status === 'active' ? 'border-l-blue-500' :
                          'border-l-gray-300'
                        }`}>
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-white ${
                                  step.status === 'complete' ? 'bg-green-500' :
                                  step.status === 'active' ? 'bg-blue-500' :
                                  'bg-gray-300'
                                }`}>
                                  {step.id}
                                </div>
                                <CardTitle className="text-lg">{step.title}</CardTitle>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground">
                              {step.description}
                            </p>
                          </CardContent>
                        </Card>
                        {index < processSteps.length - 1 && (
                          <div className="hidden md:flex items-center justify-center p-2">
                            <ChevronRight size={20} className="text-muted-foreground" />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Sector-specific information */}
                <div className="mt-10">
                  <h2 className="text-2xl font-semibold mb-6">
                    {sectorOptions.find(s => s.value === selectedSector)?.label || selectedSector} Climate Risk Profile
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle>Key Physical Risks</CardTitle>
                        <CardDescription>
                          Primary climate-related physical risks affecting this sector
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div>
                          <h4 className="font-medium">Supply Chain Disruption</h4>
                          <p className="text-sm text-muted-foreground">Extreme weather events disrupting raw material supply and logistics networks</p>
                        </div>
                        <div>
                          <h4 className="font-medium">Infrastructure Damage</h4>
                          <p className="text-sm text-muted-foreground">Damage to manufacturing facilities and transportation networks from extreme weather</p>
                        </div>
                        <div>
                          <h4 className="font-medium">Water Stress</h4>
                          <p className="text-sm text-muted-foreground">Reduced water availability affecting production processes</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle>Key Transition Risks</CardTitle>
                        <CardDescription>
                          Primary climate-related transition risks affecting this sector
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div>
                          <h4 className="font-medium">Carbon Pricing</h4>
                          <p className="text-sm text-muted-foreground">Increasing costs from carbon taxes and emissions trading schemes</p>
                        </div>
                        <div>
                          <h4 className="font-medium">Technology Disruption</h4>
                          <p className="text-sm text-muted-foreground">Risk of assets becoming stranded due to rapid shifts toward low-carbon alternatives</p>
                        </div>
                        <div>
                          <h4 className="font-medium">Changing Market Demands</h4>
                          <p className="text-sm text-muted-foreground">Shifting consumer and B2B preferences toward low-carbon products</p>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card className="md:col-span-2">
                      <CardHeader>
                        <CardTitle>Key Opportunities</CardTitle>
                        <CardDescription>
                          Climate-related opportunities for value creation
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <h4 className="font-medium">Resource Efficiency</h4>
                            <p className="text-sm text-muted-foreground">Energy efficiency improvements and circular production models</p>
                          </div>
                          <div>
                            <h4 className="font-medium">Low-Carbon Products</h4>
                            <p className="text-sm text-muted-foreground">Development of climate-friendly alternatives using green technologies</p>
                          </div>
                          <div>
                            <h4 className="font-medium">Resilient Operations</h4>
                            <p className="text-sm text-muted-foreground">Enhanced supply chain resilience and operational adaptability</p>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter>
                        <Button variant="outline" className="w-full">Start Detailed Assessment</Button>
                      </CardFooter>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </main>
          
          <Footer />
        </div>
      </SidebarProvider>
    </div>
  );
};

export default ClimateRiskAssessment;
