import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UserRound, Mail, Building, MapPin, Save, Key, X, Phone, Shield } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from '@/hooks/use-toast';

interface UserData {
  name: string;
  email: string;
  phone_number?: string;
  company: string;
  is_active: boolean;
  uuid: string;
  role?: string;
  location?: string; // Added for compatibility with existing UI
}

const Profile = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [userProfile, setUserProfile] = useState<UserData>({
    name: "",
    email: "",
    phone_number: "",
    company: "",
    is_active: false,
    uuid: "",
    role: "",
    location: ""
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({...userProfile});

  // Load user data from localStorage when component mounts
  useEffect(() => {
    const userData = localStorage.getItem('userData');
    const token = localStorage.getItem('accessToken');
    
    if (!userData || !token) {
      toast({
        title: "Authentication required",
        description: "Please log in to view your profile",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    try {
      const parsedUserData = JSON.parse(userData);
      setUserProfile({
        ...parsedUserData,
        location: parsedUserData.location || "", // Handle possible undefined
      });
      setEditForm({
        ...parsedUserData,
        location: parsedUserData.location || "", // Handle possible undefined
      });
    } catch (e) {
      console.error("Error parsing user data", e);
      localStorage.removeItem('userData');
      localStorage.removeItem('accessToken');
      navigate('/login');
    }
  }, [navigate, toast]);

  const handleLogout = () => {
    localStorage.removeItem('userData');
    localStorage.removeItem('accessToken');
    navigate('/login');
    toast({
      title: "Logged out",
      description: "You have been successfully logged out"
    });
  };

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const token = localStorage.getItem('accessToken');
    if (!token) {
      toast({
        title: "Authentication required",
        description: "Please log in again to update your profile",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    try {
      const response = await fetch('https://api.recre8.earth/update-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(editForm)
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      setUserProfile({...editForm});
      localStorage.setItem('userData', JSON.stringify(editForm));
      setIsEditing(false);
      
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully."
      });
    } catch (err) {
      toast({
        title: "Update failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleCancelEdit = () => {
    setEditForm({...userProfile});
    setIsEditing(false);
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords don't match.",
        variant: "destructive"
      });
      return;
    }

    const token = localStorage.getItem('accessToken');
    if (!token) {
      toast({
        title: "Authentication required",
        description: "Please log in again to change your password",
        variant: "destructive",
      });
      navigate('/login');
      return;
    }

    try {
      const response = await fetch('https://api.recre8.earth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          current_password: passwordForm.currentPassword,
          new_password: passwordForm.newPassword
        })
      });

      if (!response.ok) {
        throw new Error('Failed to change password');
      }
      
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully."
      });
      
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err) {
      toast({
        title: "Password change failed",
        description: "Failed to change password. Please check your current password and try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="max-w-7xl mx-auto w-full px-4 py-8">
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8">
          <TabsTrigger value="profile">Profile Information</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <Card className="lg:col-span-1">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarFallback className="text-3xl bg-recrea-turquoise text-white">
                      {userProfile.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <CardTitle>{userProfile.name}</CardTitle>
                <CardDescription>{userProfile.role || 'User'}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-recrea-turquoise" />
                  <span>{userProfile.email}</span>
                </div>
                {userProfile.phone_number && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-5 w-5 text-recrea-turquoise" />
                    <span>{userProfile.phone_number}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-recrea-turquoise" />
                  <span>{userProfile.company}</span>
                </div>
                {userProfile.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-recrea-turquoise" />
                    <span>{userProfile.location}</span>
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-recrea-turquoise" />
                  <span>Status: {userProfile.is_active ? 'Active' : 'Inactive'}</span>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="destructive" 
                  onClick={handleLogout}
                  className="w-full"
                >
                  Logout
                </Button>
              </CardFooter>
            </Card>
            
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>View your profile information</CardDescription>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <form onSubmit={handleEditSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input 
                        id="name" 
                        value={editForm.name} 
                        onChange={(e) => setEditForm({...editForm, name: e.target.value})} 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input 
                        id="email" 
                        type="email" 
                        value={editForm.email} 
                        onChange={(e) => setEditForm({...editForm, email: e.target.value})} 
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">Email cannot be changed</p>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input 
                        id="phone" 
                        value={editForm.phone_number || ''} 
                        onChange={(e) => setEditForm({...editForm, phone_number: e.target.value})} 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="company">Company</Label>
                      <Input 
                        id="company" 
                        value={editForm.company} 
                        onChange={(e) => setEditForm({...editForm, company: e.target.value})} 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input 
                        id="location" 
                        value={editForm.location || ''} 
                        onChange={(e) => setEditForm({...editForm, location: e.target.value})} 
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Input 
                        id="role" 
                        value={editForm.role || ''} 
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">Role is assigned by system administrators</p>
                    </div>
                    
                    <div className="flex justify-end space-x-2 pt-4">
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={handleCancelEdit}
                      >
                        <X className="mr-2 h-4 w-4" /> Cancel
                      </Button>
                      <Button type="submit">
                        <Save className="mr-2 h-4 w-4" /> Save Changes
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Full Name</h3>
                        <p className="mt-1">{userProfile.name}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                        <p className="mt-1">{userProfile.email}</p>
                      </div>
                      {userProfile.phone_number && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Phone Number</h3>
                          <p className="mt-1">{userProfile.phone_number}</p>
                        </div>
                      )}
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Company</h3>
                        <p className="mt-1">{userProfile.company}</p>
                      </div>
                      {userProfile.location && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground">Location</h3>
                          <p className="mt-1">{userProfile.location}</p>
                        </div>
                      )}
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Role</h3>
                        <p className="mt-1">{userProfile.role || 'User'}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">Account Status</h3>
                        <p className="mt-1">{userProfile.is_active ? 'Active' : 'Inactive'}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground">User ID</h3>
                        <p className="mt-1 text-xs">{userProfile.uuid}</p>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button onClick={() => setIsEditing(true)}>
                        Edit Profile
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="security">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle>Security</CardTitle>
                <CardDescription>Manage your account security</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Key className="h-5 w-5" />
                  <span>Last password change: Never</span>
                </div>
              </CardContent>
            </Card>
            
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>Update your password</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handlePasswordChange} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="current-password">Current Password</Label>
                    <Input 
                      id="current-password" 
                      type="password" 
                      value={passwordForm.currentPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <Input 
                      id="new-password" 
                      type="password" 
                      value={passwordForm.newPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input 
                      id="confirm-password" 
                      type="password" 
                      value={passwordForm.confirmPassword}
                      onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                    />
                  </div>
                  
                  <div className="flex justify-end pt-4">
                    <Button type="submit">
                      Change Password
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Profile;
