import React, { useState, useEffect, useContext } from 'react';
import { use<PERSON>ara<PERSON>, Link, useLocation } from 'react-router-dom';
import { FlaskConical, FlaskRound, Droplets, Cloud, ArrowLeft, ChevronDown, Check } from 'lucide-react';
import NoActivitiesFound from '@/components/NoActivitiesFound';
import { ConnectionDetailsModal } from '@/components/ConnectionDetailsModal';
import { getActivitySlug } from '@/utils/slug';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import ExpertiseSection from '@/components/dashboard/ExpertiseSection';
import experts from '@/data/expertiseData';
import { useRegions } from '@/hooks/useRegions';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { fetchActivityNameTechnologies } from '@/services/connectionFormApi';
import { Skeleton } from '@/components/ui/skeleton';
import { SectorActivitiesContext } from '@/contexts/SectorActivitiesContext';

const dummyActivity = [
  {
    "uuid": "019744b1-ee78-72b7-99c1-69e513357d19",
    "title": "Boiler",
    "subtext": null,
    "description": null,
    "created_at": "2025-06-06T10:03:21.073619",
    "updated_at": "2025-06-06T10:03:21.073619"
  },
  {
    "uuid": "019744b6-9ef6-7bc7-a25f-923d2e7dca5e",
    "title": "Turbine",
    "subtext": null,
    "description": null,
    "created_at": "2025-06-06T10:08:28.403577",
    "updated_at": "2025-06-06T10:08:28.403577"
  },
  {
    "uuid": "019776c7-8042-79ff-b5b4-29df00d58f56",
    "title": "Test",
    "subtext": null,
    "description": null,
    "created_at": "2025-06-16T03:27:55.455760",
    "updated_at": "2025-06-16T03:27:55.455760"
  }
]

const ActivityDetails = () => {
  const { activityName } = useParams<{ activityName: string }>();
  const location = useLocation();
  const urlParams = new URLSearchParams(location.search);
  const sector = urlParams.get('sector') || 'steel';
  const {
    activities,
    loadingActivities,
    activitiesError,
  } = useContext(SectorActivitiesContext);
  const [activity, setActivity] = useState<any>(null);
  const [technologies, setTechnologies] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openIdx, setOpenIdx] = useState(0);
  const [selectedTab, setSelectedTab] = useState('technologies');
  const [selectedRegion, setSelectedRegion] = useState('global');
  const { regions, loadingRegions, regionsError, fetchRegionsNow } = useRegions(false);

  useEffect(() => {
    const fetchActivity = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch all activities for the sector
        // const activities = await fetchActivities(sector);
        // Find the activity by slug
        const activityItem = activities.find(item => getActivitySlug(item.name) === activityName);
        console.log("activityItem :", activityItem, activities);
        setActivity(activityItem);
        if (activityName) {
          const techs = await fetchActivityNameTechnologies(activityName?.toLowerCase());
          // const techs = await fetchActivityNameTechnologies("Condensation");
          setTechnologies(techs);
        } else {
          setTechnologies([]);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch activity details');
      } finally {
        setLoading(false);
      }
    };
    fetchActivity();
  }, [activityName, activities, sector]);

  // Only fetch regions if expertise tab is selected and regions are not loaded
  useEffect(() => {
    if (selectedTab === 'expertise' && regions.length === 0) {
      fetchRegionsNow();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedTab]);

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 md:px-8 py-8">
        <Skeleton className="h-12 w-1/3 mb-6 rounded-xl" />
        <div className="flex gap-4 mb-4">
          <Skeleton className="h-16 w-16 rounded-lg" />
          <div className="flex-1">
            <Skeleton className="h-8 w-2/3 mb-2 rounded" />
            <Skeleton className="h-4 w-1/2 rounded" />
          </div>
        </div>
        <Skeleton className="h-10 w-1/4 mb-4 rounded" />
        <div className="space-y-3">
          {[...Array(3)].map((_, idx) => (
            <Skeleton key={idx} className="h-20 w-full rounded-lg" />
          ))}
        </div>
      </div>
    );
  }

  // Filter experts by selected region
  const filteredExperts = selectedRegion === 'global'
    ? experts
    : experts.filter(expert => expert.region && expert.region.code && expert.region.code.toLowerCase() === selectedRegion.toLowerCase());

  return (
    <div className="max-w-7xl mx-auto px-4 md:px-8 py-8">
      {/* Back Link */}
      <Link to={`/marketplace?sector=${sector}`} className="text-muted-foreground text-sm flex items-center mb-6 gap-2 no-underline hover:no-underline focus:no-underline">
        <ArrowLeft className="h-4 w-4" /> Back to Marketplace
      </Link>
      {/* Activity Details */}
      <div className="flex items-center gap-4 mb-4">
        <div className="bg-recrea-light-mint rounded-lg p-4 flex items-center justify-center">
          <span className="text-2xl">{activity?.icon || <FlaskConical />}</span>
        </div>
        <div>
          <h1 className="text-3xl font-bold mb-1 break-words">{activity?.name}</h1>
          <p className="text-muted-foreground mb-1">
            {activity?.subtext ? activity.subtext : <span className="text-gray-400">No description available.</span>}
          </p>
          {/* <p className="font-bold text-base"><span className="font-bold">{activity.numberOfTech}</span> Technologies Available</p> */}
        </div>
      </div>
      {error || !activity ? 
        <NoActivitiesFound />
      :
        <>
          {/* Tabs Section */}
          <div className="flex items-center justify-between mt-8 mb-2">
            <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1">
              <TabsList className="mb-2">
                <TabsTrigger value="technologies">Technologies</TabsTrigger>
                <TabsTrigger value="expertise">Expertise</TabsTrigger>
              </TabsList>
            </Tabs>
            {selectedTab === 'expertise' && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-4 flex items-center gap-2">
                    {loadingRegions
                      ? 'Loading regions...'
                      : (regions.find(r => r.code === selectedRegion)?.name || 'Select Region')}
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Region</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {loadingRegions ? (
                    <DropdownMenuItem disabled>Loading regions...</DropdownMenuItem>
                  ) : regionsError ? (
                    <DropdownMenuItem disabled>{regionsError}</DropdownMenuItem>
                  ) : regions.length === 0 ? (
                    <DropdownMenuItem disabled>No regions available</DropdownMenuItem>
                  ) : (
                    regions.map(region => (
                      <DropdownMenuItem
                        key={region.code}
                        className="flex items-center justify-between"
                        onClick={() => setSelectedRegion(region.code)}
                      >
                        {region.name}
                        {selectedRegion === region.code && (
                          <Check className="h-4 w-4" />
                        )}
                      </DropdownMenuItem>
                    ))
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="mt-0">
            <TabsContent value="technologies">
              <div className="bg-white rounded-xl border p-6 md:p-8 shadow-sm">
                <h2 className="text-2xl font-bold mb-2">Available Technologies</h2>
                <p className="text-muted-foreground mb-6">Explore the technologies associated with {activity.name}</p>
                <div className="space-y-3">
                  {technologies.map((tech, idx) => {
                    const techSlug = getActivitySlug(tech.title);
                    return (
                      <div key={idx} className="border rounded-lg">
                        <button
                          className="w-full text-left px-4 py-3 focus:outline-none flex flex-col"
                          onClick={() => setOpenIdx(openIdx === idx ? -1 : idx)}
                          aria-expanded={openIdx === idx}
                        >
                          <div className="font-semibold text-base flex items-center justify-between">
                            <span>{tech.title}</span>
                            <span className={`ml-2 transition-transform ${openIdx === idx ? 'rotate-180' : ''}`}>▼</span>
                          </div>
                          <div className="text-muted-foreground text-sm">
                            {tech.subtext}
                          </div>
                        </button>
                        {openIdx === idx && (
                          <div className="px-4 pb-4 pt-2">
                            <div className="text-sm mb-2">
                              {tech.description
                                ? tech.description + ' This technology is a key enabler for decarbonizing the steel industry, offering significant operational and environmental benefits. It is widely adopted in modern steel plants and continues to evolve with new innovations in energy efficiency and emissions reduction.'
                                : <span className="text-gray-400">No description available.</span>
                              }
                            </div>
                            <button
                              className="mt-2 bg-green-600 text-white px-4 py-2 rounded font-semibold text-sm hover:bg-green-700 transition"
                              onClick={() => window.location.href = `/marketplace/${activityName}/${techSlug}?sector=${sector}`}
                            >
                              Learn More
                            </button>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </TabsContent>
            <TabsContent value="expertise">
              <ExpertiseSection experts={filteredExperts} />
            </TabsContent>
          </Tabs>
        </>
      }
    </div>
  );
};

export default ActivityDetails;
