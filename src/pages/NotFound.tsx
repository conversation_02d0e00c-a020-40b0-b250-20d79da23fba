
import { useLocation, useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';

const NotFound = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col bg-recrea-dark">
      <div className="flex flex-col items-center justify-center flex-1 w-full px-4 py-12">
        <div className="mb-8 animate-fade-in">
          <Logo />
        </div>
        <div className="w-full max-w-md animate-fade-in text-center" style={{ animationDelay: '100ms' }}>
          <div className="glass-card p-8">
            <h1 className="text-4xl font-bold mb-4 text-recrea-green">404</h1>
            <p className="text-xl text-white mb-6">Oops! Page not found</p>
            <Button 
              onClick={() => navigate('/')}
              className="primary-button"
            >
              Return to Home
            </Button>
          </div>
        </div>
      </div>
      
      <footer className="text-center py-4 text-muted-foreground text-sm">
        © 2024 recre8.earth. All rights reserved. Using AI for climate solutions.
      </footer>
    </div>
  );
};

export default NotFound;
