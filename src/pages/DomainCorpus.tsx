
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useTheme } from '@/components/ThemeProvider';
import Layout from '@/components/Layout';

type KnowledgeBase = {
  id: string;
  name: string;
};

const DomainCorpus = () => {
  const [knowledgeBases] = useState<KnowledgeBase[]>([
    { id: '1', name: 'Knowledge Base 1' },
    { id: '2', name: 'Knowledge Base 2' },
    { id: '3', name: 'Knowledge Base 3' },
  ]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const { theme } = useTheme();

  const openKnowledgeBaseDialog = (kb: KnowledgeBase) => {
    setSelectedKnowledgeBase(kb);
    setDialogOpen(true);
  };

  return (
    <Layout>
      <>
        <h1 className="text-2xl font-semibold text-recrea-turquoise">Domain Corpus</h1>
        <p className="ml-4 text-muted-foreground">Select a knowledge base for your analysis</p>
      </>

      <section className="flex-grow py-8 p-6">
        <div className="container max-w-3xl mx-auto px-4 mb-16">
          <Card className={`${theme === 'dark' ? 'glass-card' : 'bg-white shadow-lg border border-gray-200'} transition-all p-6 backdrop-blur-md`}>
            <div className="p-6">
              <div>
                <h3 className="text-xl font-semibold mb-6 text-recrea-green">
                  Select Knowledge Base
                </h3>
                <div className="space-y-4">
                  {knowledgeBases.map((kb) => (
                    <Button 
                      key={kb.id} 
                      variant="outline" 
                      className={`w-full py-6 ${theme === 'dark' 
                        ? 'border-recrea-card-border dark:bg-recrea-dark-blue/50' 
                        : 'bg-gray-50 border-gray-200 hover:bg-gray-100'} hover:border-recrea-green text-center justify-center`}
                      onClick={() => openKnowledgeBaseDialog(kb)}
                    >
                      {kb.name}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Knowledge Base Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className={`sm:max-w-md ${theme === 'dark' 
            ? 'dark:bg-recrea-dark-blue border-recrea-card-border' 
            : 'bg-white border-gray-200 shadow-xl'}`}>
          <DialogHeader>
            <DialogTitle className="text-recrea-green text-center">
              {selectedKnowledgeBase?.name}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-center mb-6">Would you like to use this knowledge base for your project?</p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={() => setDialogOpen(false)}>
                Cancel
              </Button>
              <Button className="bg-recrea-green text-recrea-dark hover:bg-recrea-green/90">
                Confirm
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default DomainCorpus;
