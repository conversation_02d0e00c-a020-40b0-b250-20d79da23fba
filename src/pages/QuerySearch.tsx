
import React, { useRef, useEffect, useState } from 'react';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useSectors } from '@/hooks/useSectors';

// Components
import SearchBar from '@/components/QuerySearch/SearchBar';
import Suggestions from '@/components/QuerySearch/Suggestions';
import SearchHistory from '@/components/QuerySearch/SearchHistory';
import ResultsDisplay from '@/components/QuerySearch/ResultsDisplay';
import FilterCategories from '@/components/QuerySearch/FilterCategories';

const QuerySearch = () => {
  const inputRef = useRef<HTMLInputElement>(null);
  const { sectors, isLoading: sectorsLoading } = useSectors();
  const [selectedSector, setSelectedSector] = useState("");
  
  // Set default sector once sectors are loaded
  useEffect(() => {
    if (sectors && sectors.length > 0 && !selectedSector) {
      setSelectedSector(sectors[0].name);
    }
  }, [sectors, selectedSector]);
  
  const {
    searchQuery,
    setSearchQuery,
    showSuggestions,
    filteredSuggestions,
    selectedCategory,
    setSelectedCategory,
    searchResults,
    showHistory,
    setShowHistory,
    searchHistory,
    savedQueries,
    hasSearched,
    isLoading,
    apiResponse,
    isJsonResponse,
    isGranular,
    setIsGranular,
    handleSearch,
    selectSuggestion,
    selectFromHistory,
    toggleSaveQuery,
    isQuerySaved,
  } = useSearchQuery();

  const categories = ['All', 'Net Zero', 'Carbon Price', 'Energy Transition', 'Decarbonization', 'Other Objectives'];
  
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const viewFullResponse = (resultId: number) => {
    import('@/components/QuerySearch/data').then(({ resultsData }) => {
      const result = resultsData.find(item => item.id === resultId);
      
      if (result) {
        import('@/hooks/use-toast').then(({ toast }) => {
          toast({
            title: result.title,
            description: result.content,
            duration: 10000,
          });
        });
      }
    });
  };
  
  const performSearch = () => {
    handleSearch(selectedSector);
  };

  return (
    <main className="flex-1 w-full">
      <div className="max-w-7xl mx-auto px-4 md:px-8 py-6 md:py-10">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">
            Query Search
          </h1>
        </div>
        <p className="text-muted-foreground mb-8">
          Search for climate-related information and insights
        </p>
        
        <div className="relative mb-6 mt-2">
          <SearchBar 
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            handleSearch={performSearch}
            isGranular={isGranular}
            setIsGranular={setIsGranular}
            selectedSector={selectedSector}
            setSelectedSector={setSelectedSector}
            sectors={sectors}
            sectorsLoading={sectorsLoading}
            inputRef={inputRef}
          />
          
          <div className="flex justify-end mt-2">
            <SearchHistory
              showHistory={showHistory}
              setShowHistory={setShowHistory}
              searchHistory={searchHistory}
              savedQueries={savedQueries}
              selectFromHistory={selectFromHistory}
              formatTime={formatTime}
            />
          </div>
          
          <Suggestions 
            showSuggestions={showSuggestions}
            filteredSuggestions={filteredSuggestions}
            selectSuggestion={selectSuggestion}
            toggleSaveQuery={toggleSaveQuery}
            isQuerySaved={isQuerySaved}
          />
        </div>
        
        {hasSearched && (
          <div className="mb-8">
            <FilterCategories 
              categories={categories}
              selectedCategory={selectedCategory}
              setSelectedCategory={setSelectedCategory}
            />
            
            <ResultsDisplay 
              isLoading={isLoading}
              apiResponse={apiResponse}
              isJsonResponse={isJsonResponse}
              selectedCategory={selectedCategory}
              searchResults={searchResults}
              viewFullResponse={viewFullResponse}
              toggleSaveQuery={toggleSaveQuery}
              isQuerySaved={isQuerySaved}
            />
          </div>
        )}
      </div>
    </main>
  );
};

export default QuerySearch;
