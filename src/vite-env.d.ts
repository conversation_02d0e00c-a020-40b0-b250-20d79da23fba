
/// <reference types="vite/client" />

// Define props for ConnectionFormDialog component
interface ConnectionFormDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onComplete?: (formData: any) => void;
  onSubmit?: (formData: any) => void;
  onClose?: () => void;
  autoFillInputs?: any[];
  sourceNode?: any;
  targetNode?: any;
  availableNodes?: any[];
  incomingConnectionData?: any;
}

// Define types for ConnectionData
interface ConnectionData {
  id: string;
  source: string;
  target: string;
  data?: any;
  type?: string;
  label?: string;
}

// Add any other types you need to reference across files
