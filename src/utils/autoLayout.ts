
/**
 * Enhanced horizontal multi-row flexible auto-layout for nodes.
 * - Nodes are placed in a horizontal grid with increased spacing.
 * - Supports multidirectional connections with appropriate spacing.
 * - Extra padding added on all sides.
 * - Second row flows right to left.
 * - Final output node is placed at the left-most position of third row.
 */
import { nodeDefaultStyle } from '@/pages/IndustryFlow.constants';

// Example node groups for horizontal layout (update as needed)
const topRow = ['2', '3', '4', '5'];
const botRow = ['6', '7', '8', '9'];
const finalRow = ['natural-gas']; // Define the final output row

export function horizontalGridAutoLayout(nodes, gapX = 350, gapY = 250, baseX = 220, baseY = 200) {
  const nodeMap = Object.fromEntries(nodes.map(n => [n.id, n]));
  let result = [];

  // Place input dots, if present
  let inputs = nodes.filter(n => n.type === 'inputdot');
  if (inputs.length === 2) {
    // input1 for top row, input2 for bot row
    result.push({
      ...inputs[0],
      position: { x: baseX - gapX * 0.6, y: baseY + gapY * 0 },
      style: { ...inputs[0].style },
      type: inputs[0].type,
    });
    result.push({
      ...inputs[1],
      position: { 
        // Place the second input dot on the right side for bottom row
        x: baseX + gapX * (botRow.length - 1) + gapX * 0.6, 
        y: baseY + gapY * 1 
      },
      style: { ...inputs[1].style },
      type: inputs[1].type,
    });
  }

  // Top row with increased spacing (left to right)
  topRow.forEach((id, idx) => {
    const node = nodeMap[id];
    if (node) {
      result.push({
        ...node,
        position: { x: baseX + gapX * idx, y: baseY },
        style: { ...nodeDefaultStyle, ...node.style },
        type: 'custom',
      });
    }
  });

  // Bottom row with increased spacing (RIGHT TO LEFT)
  botRow.forEach((id, idx) => {
    const node = nodeMap[id];
    if (node) {
      // Calculate position from right to left
      // Start from the rightmost position and move left
      const rightToLeftIdx = botRow.length - 1 - idx;
      
      result.push({
        ...node,
        position: { x: baseX + gapX * rightToLeftIdx, y: baseY + gapY },
        style: { ...nodeDefaultStyle, ...node.style },
        type: 'custom',
      });
    }
  });
  
  // Final output node in the third row (left-most position)
  finalRow.forEach((id) => {
    const node = nodeMap[id];
    if (node) {
      result.push({
        ...node,
        position: { x: baseX, y: baseY + gapY * 2 }, // Place at left-most of third row
        style: { ...nodeDefaultStyle, ...node.style },
        type: node.type, // Keep the original node type (finalOutput)
      });
    }
  });

  // Fallback for scratch/single row layouts with improved spacing
  if (result.length === 0) {
    return nodes.map((node, idx) => ({
      ...node,
      position: { x: baseX + gapX * (idx % 3), y: baseY + gapY * Math.floor(idx / 3) },
      style: { ...nodeDefaultStyle, ...node.style },
      type: 'custom',
    }));
  }

  return result;
}
