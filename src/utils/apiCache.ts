
type CacheKey = string;
type CacheData = any;

interface CacheEntry {
  data: CacheData;
  timestamp: number;
  expiresAt: number;
}

/**
 * Simple in-memory cache for API responses
 * Stores data with expiration times and provides methods for managing cache entries
 */
class ApiCache {
  private cache: Record<CacheKey, CacheEntry> = {};
  private readonly DEFAULT_TTL_MS = 5 * 60 * 1000; // 5 minutes default TTL
  
  /**
   * Set a value in the cache with optional TTL
   */
  set(key: CacheKey, data: CacheData, ttlMs?: number): void {
    const timestamp = Date.now();
    const expiresAt = timestamp + (ttlMs || this.DEFAULT_TTL_MS);
    
    this.cache[key] = {
      data,
      timestamp,
      expiresAt
    };
    
    console.log(`[ApiCache] Set cache for key: ${key}, expires in ${((expiresAt - timestamp) / 1000).toFixed(1)}s`);
  }
  
  /**
   * Get a value from the cache if it exists and hasn't expired
   */
  get<T = CacheData>(key: <PERSON><PERSON><PERSON><PERSON>): T | null {
    const entry = this.cache[key];
    
    // Return null if entry doesn't exist
    if (!entry) {
      console.log(`[ApiCache] Cache miss for key: ${key}`);
      return null;
    }
    
    const now = Date.now();
    
    // Check if entry has expired
    if (now > entry.expiresAt) {
      console.log(`[ApiCache] Cache expired for key: ${key}`);
      delete this.cache[key];
      return null;
    }
    
    console.log(`[ApiCache] Cache hit for key: ${key}, expires in ${((entry.expiresAt - now) / 1000).toFixed(1)}s`);
    return entry.data as T;
  }
  
  /**
   * Check if a key exists in the cache and is still valid
   */
  has(key: CacheKey): boolean {
    const entry = this.cache[key];
    if (!entry) return false;
    
    const now = Date.now();
    if (now > entry.expiresAt) {
      delete this.cache[key];
      return false;
    }
    
    return true;
  }
  
  /**
   * Remove a specific key from the cache
   */
  remove(key: CacheKey): void {
    if (this.cache[key]) {
      delete this.cache[key];
      console.log(`[ApiCache] Removed cache for key: ${key}`);
    }
  }
  
  /**
   * Remove all keys matching a prefix
   */
  removeByPrefix(prefix: string): void {
    const keysToRemove = Object.keys(this.cache).filter(key => key.startsWith(prefix));
    keysToRemove.forEach(key => delete this.cache[key]);
    console.log(`[ApiCache] Removed ${keysToRemove.length} cache entries with prefix: ${prefix}`);
  }
  
  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache = {};
    console.log('[ApiCache] Cache cleared');
  }
  
  /**
   * Get a value from cache if it exists, otherwise fetch it using the provided function
   * and store the result in the cache
   */
  async getOrFetch<T>(
    key: CacheKey, 
    fetchFn: () => Promise<T>, 
    ttlMs?: number
  ): Promise<T> {
    // Try to get from cache
    const cachedData = this.get<T>(key);
    if (cachedData !== null) {
      return cachedData;
    }
    
    // If not in cache, fetch it
    console.log(`[ApiCache] Fetching data for key: ${key}`);
    const data = await fetchFn();
    
    // Cache the result if it's not null
    if (data !== null && data !== undefined) {
      this.set(key, data, ttlMs);
    }
    
    return data;
  }
}

// Export a singleton instance to be used throughout the app
export const apiCache = new ApiCache();

// Helper to generate consistent cache keys
export const generateCacheKey = (base: string, params: Record<string, any>): string => {
  const paramStrings = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null && value !== '')
    .map(([key, value]) => `${key}:${value}`)
    .sort()
    .join(',');
    
  return `${base}${paramStrings ? `|${paramStrings}` : ''}`;
};
