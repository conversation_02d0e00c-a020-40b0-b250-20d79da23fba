
import { useToast } from "@/hooks/use-toast";

interface SearchRequest {
  query: string;
  search_type: "local" | "global";
  technology_sector: string;
  question_type: string;
}

interface SearchResponse {
  response: string | object;
  matches?: Array<any>;
}

interface ToastUtils {
  toast: ReturnType<typeof useToast>["toast"];
  dismiss: ReturnType<typeof useToast>["dismiss"];
  toasts: ReturnType<typeof useToast>["toasts"];
}

export const callSearchApi = async (
  requestPayload: SearchRequest,
  toastUtils: ToastUtils
): Promise<SearchResponse | null> => {
  try {
    const accessToken = localStorage.getItem('accessToken');
    
    if (!accessToken) {
      toastUtils.toast({
        title: "Authentication Error",
        description: "No access token found. Please log in again.",
        variant: "destructive"
      });
      return null;
    }
    
    console.log("Sending search request:", JSON.stringify(requestPayload, null, 2));
    
    // Make real API call to the specified endpoint
    const response = await fetch('https://api.recre8.earth/ask', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    });
    
    if (!response.ok) {
      console.error("Search API error. Status:", response.status);
      const errorText = await response.text();
      console.error("Error response:", errorText);
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }
    
    const data: SearchResponse = await response.json();
    console.log("Search API response:", data);
    
    return data;
  } catch (error: any) {
    console.error("Search API error:", error);
    toastUtils.toast({
      title: "Search Error",
      description: error.message || "Failed to fetch search results",
      variant: "destructive"
    });
    return null;
  }
};

export interface ClimateRisk {
  id?: number;
  title: string;
  category?: string;
  type?: string;
  description: string;
  relevance?: string;
  severity?: string;
  impact_level?: string;
  impacts?: string[];
}

export const fetchClimateRisks = async (
  sector: string,
  toastUtils: ToastUtils
): Promise<ClimateRisk[] | null> => {
  try {
    const requestPayload: SearchRequest = {
      query: "What are the major climate risks associated with this sector?",
      search_type: "local",
      technology_sector: sector,
      question_type: "Climate Risks"
    };

    const data = await callSearchApi(requestPayload, toastUtils);
    
    if (!data || !data.response) {
      throw new Error("No data received from the API");
    }

    // Parse the response to extract climate_risks
    let risksData: ClimateRisk[] = [];
    
    try {
      // If response is a string (JSON string), parse it
      if (typeof data.response === 'string') {
        const parsedResponse = JSON.parse(data.response);
        risksData = parsedResponse.climate_risks || [];
      } 
      // If response is already an object
      else if (typeof data.response === 'object') {
        const responseObj = data.response as any;
        risksData = responseObj.climate_risks || [];
      }
    } catch (parseError) {
      console.error("Error parsing climate risks:", parseError);
      throw new Error("Failed to parse climate risks data");
    }

    // Add IDs to risks if they don't have them
    risksData = risksData.map((risk, index) => ({
      ...risk,
      id: risk.id || index + 1
    }));
    
    console.log("Parsed climate risks:", risksData);
    return risksData;
  } catch (error: any) {
    console.error("Climate risks fetch error:", error);
    toastUtils.toast({
      title: "Data Fetch Error",
      description: error.message || "Failed to fetch climate risks",
      variant: "destructive"
    });
    return null;
  }
};

export interface Technology {
  id?: number;
  title: string;
  description: string;
  maturity: string;
  impact: string;
  value?: string | number;
}

export const fetchTechnologies = async (
  sector: string,
  toastUtils: ToastUtils
): Promise<Technology[] | null> => {
  try {
    const requestPayload: SearchRequest = {
      query: `What are the key technologies in ${sector} manufacturing?`,
      search_type: "local",
      technology_sector: sector,
      question_type: "Technologies"
    };

    const data = await callSearchApi(requestPayload, toastUtils);
    
    if (!data || !data.response) {
      throw new Error("No data received from the API");
    }

    // Parse the response to extract decarbonization_technologies
    let technologiesData: Technology[] = [];
    
    try {
      // If response is a string (JSON string), parse it
      if (typeof data.response === 'string') {
        const parsedResponse = JSON.parse(data.response);
        technologiesData = parsedResponse.decarbonization_technologies || [];
      } 
      // If response is already an object
      else if (typeof data.response === 'object') {
        const responseObj = data.response as any;
        technologiesData = responseObj.decarbonization_technologies || [];
      }
    } catch (parseError) {
      console.error("Error parsing technologies:", parseError);
      throw new Error("Failed to parse technologies data");
    }

    // Add IDs to technologies if they don't have them
    technologiesData = technologiesData.map((tech, index) => ({
      ...tech,
      id: tech.id || index + 1
    }));
    
    console.log("Parsed technologies:", technologiesData);
    return technologiesData;
  } catch (error: any) {
    console.error("Technologies fetch error:", error);
    toastUtils.toast({
      title: "Data Fetch Error",
      description: error.message || "Failed to fetch technologies",
      variant: "destructive"
    });
    return null;
  }
};
