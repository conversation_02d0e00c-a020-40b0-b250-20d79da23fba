
interface CachedSummary {
  content: string;
  timestamp: number;
  tileId: string;
  tab: string;
  sector: string;
}

interface CachedChatHistory {
  messages: import('@/types/TopicDetail').ChatMessage[];
  timestamp: number;
}

class TopicCache {
  private summaryCache = new Map<string, CachedSummary>();
  private chatCache = new Map<string, CachedChatHistory>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private generateSummaryKey(tileId: string, tab: string, sector: string): string {
    return `summary-${tileId}-${tab}-${sector}`;
  }

  private generateChatKey(tileId: string): string {
    return `chat-${tileId}`;
  }

  // Summary cache methods
  getSummary(tileId: string, tab: string, sector: string): string | null {
    const key = this.generateSummaryKey(tileId, tab, sector);
    const cached = this.summaryCache.get(key);
    
    if (!cached) return null;
    
    // Check if cache is still valid
    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      this.summaryCache.delete(key);
      return null;
    }
    
    console.log(`[TopicCache] Summary cache hit for: ${key}`);
    return cached.content;
  }

  setSummary(tileId: string, tab: string, sector: string, content: string): void {
    const key = this.generateSummaryKey(tileId, tab, sector);
    this.summaryCache.set(key, {
      content,
      timestamp: Date.now(),
      tileId,
      tab,
      sector
    });
    console.log(`[TopicCache] Summary cached for: ${key}`);
  }

  // Chat cache methods
  getChatHistory(tileId: string): import('@/types/TopicDetail').ChatMessage[] {
    const key = this.generateChatKey(tileId);
    const cached = this.chatCache.get(key);
    
    if (!cached) {
      console.log(`[TopicCache] No chat history for: ${key}`);
      return [];
    }
    
    console.log(`[TopicCache] Chat history loaded for: ${key}, ${cached.messages.length} messages`);
    return cached.messages;
  }

  setChatHistory(tileId: string, messages: import('@/types/TopicDetail').ChatMessage[]): void {
    const key = this.generateChatKey(tileId);
    this.chatCache.set(key, {
      messages,
      timestamp: Date.now()
    });
    console.log(`[TopicCache] Chat history saved for: ${key}, ${messages.length} messages`);
  }

  // Clear methods
  clearSummaryCache(): void {
    this.summaryCache.clear();
    console.log('[TopicCache] Summary cache cleared');
  }

  clearChatCache(): void {
    this.chatCache.clear();
    console.log('[TopicCache] Chat cache cleared');
  }

  clearAll(): void {
    this.clearSummaryCache();
    this.clearChatCache();
  }
}

export const topicCache = new TopicCache();
