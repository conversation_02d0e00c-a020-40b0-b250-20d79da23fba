
/**
 * Utility functions for handling and creating connections/edges in the flow diagram
 */

import { v4 as uuidv4 } from "uuid";
import { OutputForm, EnergyOutput, MaterialOutput, EnergyByProduct, MaterialByProduct } from "@/components/ConnectionForm/types";
import { edgeStyle, edgeLabelStyle } from "@/pages/IndustryFlow.constants";
import { MarkerType } from '@xyflow/react';

// Interface for a standardized connection object
export interface ConnectionItem {
  id: string;
  source: string;
  target: string | null; // null for standalone outputs with no target
  label: string;
  animated?: boolean;
  labelStyle?: Record<string, any>;
  style?: Record<string, any>;
  type?: string; // Edge type (e.g., 'smoothstep')
  markerEnd?: any; // Arrow marker
  isFinal?: boolean;
  data?: {
    outputType: string;
    quantity?: string;
    unit?: string;
    technology?: string;
    sec?: string;
    smc?: string;
    type?: string; // Allow type property in data
    other?: Record<string, any>;
  };
}

/**
 * Prepares connection objects from energy outputs
 */
export const prepareEnergyOutputConnections = (
  outputs: OutputForm[],
  sourceNodeId: string
): ConnectionItem[] => {
  if (!outputs || outputs.length === 0 || !sourceNodeId) return [];
  
  const connections: ConnectionItem[] = [];
  
  outputs.forEach(output => {
    if (!output.outputTechnology) return;
    
    // Process valid energy outputs
    output.energyOutputs.forEach(energyOutput => {
      // Create connections for outputs that have energy name
      // For final outputs, set target to null (standalone)
      // For non-final outputs, use the connect property as target
      if (energyOutput.energy) {
        // For final outputs, set target to null (standalone)
        // For non-final outputs, use the connect property as target
        const targetId = energyOutput.final ? null : energyOutput.connect;

        // Skip if non-final output has no target
        if (!energyOutput.final && !targetId) return;
        
        // Create unique ID for this connection
        const connectionId = `energy-${uuidv4().substring(0, 8)}`;
        
        connections.push({
          id: connectionId,
          source: sourceNodeId,
          target: targetId,
          label: energyOutput.energy, // Use energy name as label
          style: edgeStyle,
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
          data: {
            outputType: "energy",
            quantity: energyOutput.qty,
            unit: energyOutput.unit,
            technology: output.outputTechnology,
            sec: energyOutput.sec,
            other: {
              outputId: output.id,
              energyOutputId: energyOutput.id
            }
          }
        });
      }
    });
    
    // Process valid material outputs
    output.matOutputs.forEach(matOutput => {
      // Create connections for outputs that have material name
      // For final outputs, set target to null (standalone)
      // For non-final outputs, use the connect property as target
      if (matOutput.material) {
        // For final outputs, set target to null (standalone)
        // For non-final outputs, use the connect property as target
        const targetId = matOutput.final ? null : matOutput.connect;

        // Skip if non-final output has no target
        if (!matOutput.final && !targetId) return;
        
        // Create unique ID for this connection
        const connectionId = `material-${uuidv4().substring(0, 8)}`;
        
        connections.push({
          id: connectionId,
          source: sourceNodeId,
          target: targetId,
          label: matOutput.material, // Use material name as label
          style: edgeStyle,
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" },
          data: {
            outputType: "material",
            quantity: matOutput.qty,
            unit: matOutput.unit,
            technology: output.outputTechnology,
            smc: matOutput.smc,
            other: {
              outputId: output.id,
              materialOutputId: matOutput.id
            }
          }
        });
      }
    });
  });
  
  return connections;
};

/**
 * Creates connection objects from byproducts
 */
export const prepareByProductConnections = (
  energyByProducts: EnergyByProduct[],
  materialByProducts: MaterialByProduct[],
  sourceNodeId: string,
  technology: string
): ConnectionItem[] => {
  if (!sourceNodeId) return [];
  
  const connections: ConnectionItem[] = [];
  
  // Process energy byproducts
  if (energyByProducts && energyByProducts.length > 0) {
    energyByProducts.forEach((byproduct, index) => {
      // Only create connection if byproduct name exists and connect is set
      if (byproduct.byproduct && byproduct.connect) {
        // Generate unique ID
        const connectionId = `energybp-${uuidv4().substring(0, 8)}`;
        
        // Handle special "null" case for standalone outputs
        const target = byproduct.connect === "null" ? null : byproduct.connect;
        
        connections.push({
          id: connectionId,
          source: sourceNodeId,
          target: target,
          label: byproduct.byproduct,
          animated: true, // Animate byproduct connections
          style: { ...edgeStyle, stroke: "#4ade80" }, // Green color for energy byproducts
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#4ade80" },
          data: {
            outputType: "energy-byproduct",
            quantity: byproduct.bppo,
            unit: byproduct.unit,
            technology: technology,
            other: {
              replaced: byproduct.replaced,
              index: index,
              standalone: target === null
            }
          }
        });
      }
    });
  }
  
  // Process material byproducts
  if (materialByProducts && materialByProducts.length > 0) {
    materialByProducts.forEach((byproduct, index) => {
      // Only create connection if byproduct name exists and connect is set
      if (byproduct.byproduct && byproduct.connect) {
        // Generate unique ID
        const connectionId = `materialbp-${uuidv4().substring(0, 8)}`;
        
        // Handle special "null" case for standalone outputs
        const target = byproduct.connect === "null" ? null : byproduct.connect;
        
        connections.push({
          id: connectionId,
          source: sourceNodeId,
          target: target,
          label: byproduct.byproduct,
          animated: true, // Animate byproduct connections
          style: { ...edgeStyle, stroke: "#c084fc" }, // Purple color for material byproducts
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#c084fc" },
          data: {
            outputType: "material-byproduct",
            quantity: byproduct.bppo,
            unit: byproduct.unit,
            technology: technology,
            other: {
              replaced: byproduct.replaced,
              techEmissionFactor: byproduct.techEmissionFactor,
              emissionFactor: byproduct.emissionFactor,
              emissionUnit: byproduct.emissionUnit,
              index: index,
              standalone: target === null
            }
          }
        });
      }
    });
  }
  
  return connections;
};

/**
 * Checks if an edge already exists between two nodes with the same label
 */
export const isDuplicateEdge = (
  existingEdges: any[],
  source: string,
  target: string | null,
  label: string
): boolean => {
  return existingEdges.some(edge => 
    edge.source === source && 
    edge.target === target && 
    edge.label === label
  );
};

/**
 * Prepares input connections from energy and material inputs with "Nil" source activity
 */
export const prepareInputConnections = (
  formData: any,
  targetNodeId: string
): ConnectionItem[] => {
  const connections: ConnectionItem[] = [];

  // Process energy inputs with "Nil" source activity
  if (formData.energyInputs && Array.isArray(formData.energyInputs)) {
    formData.energyInputs.forEach((energyInput: any) => {
      if (energyInput.sourceActivity === "Nil" && energyInput.source) {
        const connectionId = `input-energy-${uuidv4().substring(0, 8)}`;
        connections.push({
          id: connectionId,
          source: null, // No source node for input edges
          target: targetNodeId,
          label: energyInput.source,
          style: { ...edgeStyle, stroke: "#10B981" }, // Green color for input edges
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
          animated: false,
          data: {
            outputType: 'input-energy',
            type: 'input-energy',
            quantity: energyInput.cost,
            unit: energyInput.unit,
            technology: energyInput.technology,
            sec: energyInput.sec,
            other: {
              energy: energyInput.source,
              sourceActivity: energyInput.sourceActivity
            }
          }
        });
      }
    });
  }

  // Process material inputs with "Nil" source activity
  if (formData.materialInputs && Array.isArray(formData.materialInputs)) {
    formData.materialInputs.forEach((materialInput: any) => {
      if (materialInput.sourceActivity === "Nil" && materialInput.material) {
        const connectionId = `input-material-${uuidv4().substring(0, 8)}`;
        connections.push({
          id: connectionId,
          source: null, // No source node for input edges
          target: targetNodeId,
          label: materialInput.material,
          style: { ...edgeStyle, stroke: "#10B981" }, // Green color for input edges
          labelStyle: edgeLabelStyle,
          type: 'smoothstep',
          markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
          animated: false,
          data: {
            outputType: 'input-material',
            type: 'input-material',
            quantity: materialInput.cost,
            unit: materialInput.unit,
            technology: materialInput.technology,
            smc: materialInput.smc,
            other: {
              material: materialInput.material,
              sourceActivity: materialInput.sourceActivity
            }
          }
        });
      }
    });
  }

  return connections;
};

/**
 * Processes form data and returns all valid connections
 */
export const processConnectionsFromFormData = (
  formData: any,
  sourceNodeId: string,
  existingEdges: any[]
): ConnectionItem[] => {
  const connections: ConnectionItem[] = [];

  // Process input connections (for inputs with "Nil" source activity)
  // For input connections, the sourceNodeId is actually the target node
  const inputConnections = prepareInputConnections(formData, sourceNodeId);
  connections.push(...inputConnections);

  // Process regular outputs
  if (formData.outputs && Array.isArray(formData.outputs)) {
    const outputConnections = prepareEnergyOutputConnections(
      formData.outputs,
      sourceNodeId
    );

    // Filter out duplicates
    const nonDuplicateOutputs = outputConnections.filter(
      conn => !isDuplicateEdge(existingEdges, conn.source, conn.target || "", conn.label)
    );

    connections.push(...nonDuplicateOutputs);
  }
  
  // Process byproducts
  const energyByProducts = formData.energyByProducts 
    ? (typeof formData.energyByProducts === 'string' 
        ? JSON.parse(formData.energyByProducts) 
        : formData.energyByProducts)
    : [];
    
  const materialByProducts = formData.materialByProducts 
    ? (typeof formData.materialByProducts === 'string' 
        ? JSON.parse(formData.materialByProducts) 
        : formData.materialByProducts)
    : [];
  
  if ((energyByProducts && energyByProducts.length > 0) || 
      (materialByProducts && materialByProducts.length > 0)) {
    
    const byproductConnections = prepareByProductConnections(
      energyByProducts,
      materialByProducts,
      sourceNodeId,
      formData.byproductTechnology || formData.technology || ""
    );
    
    // Filter out duplicates
    const nonDuplicateByproducts = byproductConnections.filter(conn => {
      if (conn.target === null) {
        // For standalone byproducts (null target), just check if a similar one exists
        return !existingEdges.some(edge => 
          edge.source === conn.source && 
          edge.target === null && 
          edge.label === conn.label
        );
      }
      return !isDuplicateEdge(existingEdges, conn.source, conn.target || "", conn.label);
    });

    connections.push(...nonDuplicateByproducts);
  }
  
  return connections;
};
