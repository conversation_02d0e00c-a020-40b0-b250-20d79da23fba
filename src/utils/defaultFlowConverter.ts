import { DefaultFlowData, De<PERSON>ult<PERSON>lowNode, DefaultFlowTechnology } from '@/hooks/useSectors';
import { DefaultNodeFormData } from '@/pages/IndustryFlow.defaultFormData';

/**
 * Converts default flow data from API format to the format used by the inventory system
 */
export const convertDefaultFlowToFormData = (defaultFlow: DefaultFlowData): Record<string, DefaultNodeFormData> => {
  const formDataMap: Record<string, DefaultNodeFormData> = {};

  Object.entries(defaultFlow.nodes).forEach(([nodeId, node]) => {
    const formData: DefaultNodeFormData = {
      formData: {},
      outputs: [],
      technologies: [],
      technologyFormData: {},
      completedAt: new Date().toISOString()
    };

    // Extract technologies
    formData.technologies = node.technologies.map(tech => tech.name);

    // Set the main formData.technology field to the first technology for compatibility
    // This ensures isTechnologySelected() works correctly
    if (node.technologies.length > 0) {
      formData.formData = {
        technology: node.technologies[0].name,
        activity: node.activity,
        currentTechnology: node.technologies[0].name
      };
    }

    // Convert each technology to the form data format
    node.technologies.forEach((tech) => {
      const techName = tech.name;
      formData.technologyFormData[techName] = {
        technology: techName,
        activity: node.activity,
        startYear: tech.startYear.toString(),
        endYear: tech.endYear.toString(),
        customTechnology: "",
        customActivity: "",
        
        // Convert material inputs
        materialInputs: tech.inputs.materials.map((material) => ({
          id: material.id,
          material: material.material,
          unit: material.unit,
          cost: material.cost?.toString() || "0",
          smc: material.specificMaterialCost?.toString() || "0",
          sourceActivity: material.sourceNodeId || "Nil",
          technology: material.sourceTechnology || "Nil"
        })),
        
        // Convert energy inputs
        energyInputs: tech.inputs.energies.map((energy) => ({
          id: energy.id,
          energy: energy.energy,
          unit: energy.unit,
          cost: energy.cost?.toString() || "0",
          sec: energy.specificEnergyCost?.toString() || "0",
          sourceActivity: "Nil", // Default flow doesn't track energy input sources
          technology: "Nil" // Default flow doesn't track energy input sources
        })),
        
        // Convert emissions
        emissions: tech.inputs.emissions.map((emission) => ({
          id: emission.id,
          source: emission.emission,
          factor: emission.emissionFactor.toString(),
          unit: emission.unit
        })),
        
        // Legacy single entries (for compatibility)
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        
        // By-products
        byproductTechnology: techName,
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: tech.byproducts.energies.map(energy => ({
          id: energy.id,
          byproduct: energy.energy,
          unit: energy.unit,
          bppo: energy.quantity.toString(),
          connect: energy.targetNodeId || "",
          replaced: ""
        })),
        materialByProducts: tech.byproducts.materials.map(material => ({
          id: material.id,
          byproduct: material.material,
          unit: material.unit,
          bppo: material.quantity.toString(),
          connect: material.targetNodeId || "",
          replaced: ""
        })),
        
        // Financial
        financial: {
          capacity: tech.financial?.capacity?.toString() || "0",
          capacityUnit: tech.financial?.capacityUnit || "Units/day",
          capitalCostUnit: tech.financial?.capitalCostUnit || "USD",
          omCost: tech.financial?.operatingMaintenanceCost?.toString() || "0",
          operatingAndMaintenanceCost: tech.financial?.operatingMaintenanceCost?.toString() || "0" // Add both field names for compatibility
        },
        financialEntries: {}
      };

      // Convert outputs
      tech.outputs.materials.forEach((material, index) => {
        formData.outputs.push({
          id: `output-${index}`,
          targetNode: material.targetNodeId || "",
          outputTechnology: techName,
          energyOutputs: [],
          matOutputs: [{
            id: material.id,
            material: material.material,
            unit: material.unit,
            smc: material.specificMaterialCost?.toString() || "0",
            final: material.isFinalOutput || false,
            connect: material.targetNodeId || "",
            qty: material.quantity.toString(),
            qtyUnit: material.unit,
            destinationTechnology: material.targetTechnology || techName
          }]
        });
      });

      // Add energy outputs
      tech.outputs.energies.forEach((energy) => {
        const existingOutput = formData.outputs.find(output => output.targetNode === energy.targetNodeId);
        if (existingOutput) {
          existingOutput.energyOutputs.push({
            id: energy.id,
            energy: energy.energy,
            unit: energy.unit,
            sec: energy.specificEnergyCost?.toString() || "0", // Add missing SEC field
            quantity: energy.quantity.toString(),
            targetNodeId: energy.targetNodeId,
            isFinalOutput: energy.isFinalOutput || false,
            targetTechnology: energy.targetTechnology
          });
        } else {
          formData.outputs.push({
            id: `output-${formData.outputs.length}`,
            targetNode: energy.targetNodeId || "",
            outputTechnology: techName,
            energyOutputs: [{
              id: energy.id,
              energy: energy.energy,
              unit: energy.unit,
              sec: energy.specificEnergyCost?.toString() || "0", // Add missing SEC field
              quantity: energy.quantity.toString(),
              targetNodeId: energy.targetNodeId,
              isFinalOutput: energy.isFinalOutput || false,
              targetTechnology: energy.targetTechnology
            }],
            matOutputs: []
          });
        }
      });
    });

    formDataMap[nodeId] = formData;
  });

  return formDataMap;
};

/**
 * Converts default flow data to initial nodes format
 */
export const convertDefaultFlowToInitialNodes = (defaultFlow: DefaultFlowData) => {
  return Object.entries(defaultFlow.nodes).map(([nodeId, node]) => ({
    id: nodeId,
    data: { label: node.activity },
    position: node.position,
    style: {},
    type: 'custom',
  }));
}; 