
/**
 * Utility functions for handling dropdown data, especially node filtering
 */

/**
 * Filters out invalid nodes for dropdown selection
 * - Removes "input1" nodes which are system-reserved
 * - Removes nodes without an ID
 * - Ensures nodes have data and labels for display
 */
export const filterValidNodes = (nodes: any[] = []): any[] => {
  if (!nodes || !Array.isArray(nodes)) return [];
  
  return nodes.filter(node => {
    // Check if the node has an ID and it's not the system "input1" node
    return node && 
           node.id && 
           node.id.trim() !== "" && 
           node.id !== "input1";
  });
};

/**
 * Gets a node label or ID for display purposes
 */
export const getNodeLabel = (node: any): string => {
  if (!node) return "Unknown node";
  
  return node.data?.label || node.id || "Unnamed node";
};

/**
 * Checks if a node is valid for selection in a dropdown
 */
export const isValidNodeForSelection = (nodeId: string, availableNodes: any[]): boolean => {
  if (!nodeId) return false;
  
  // Check if the node exists in the available nodes
  return availableNodes.some(node => node.id === nodeId);
};
