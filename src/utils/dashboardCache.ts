import { ChatMessage } from '@/types/TopicDetail';

interface CachedResponse {
  content: string;
  timestamp: number;
}

interface CachedChat {
  messages: ChatMessage[];
  timestamp: number;
}

class DashboardCache {
  private static instance: DashboardCache;
  private responseCache = new Map<string, CachedResponse>();
  private chatCache = new Map<string, CachedChat>();
  private readonly CACHE_DURATION = 1000 * 60 * 30; // 30 minutes

  private constructor() {}

  static getInstance(): DashboardCache {
    if (!DashboardCache.instance) {
      DashboardCache.instance = new DashboardCache();
    }
    return DashboardCache.instance;
  }

  private generateCacheKey(tileId: string, tab: string, sector: string): string {
    return `${tileId}-${tab}-${sector}`;
  }

  // API Response Cache Methods
  getCachedResponse(tileId: string, tab: string, sector: string): string | null {
    const key = this.generateCacheKey(tileId, tab, sector);
    const cached = this.responseCache.get(key);

    if (!cached) {
      console.log(`[DashboardCache] No cached response for: ${key}`);
      return null;
    }

    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      console.log(`[DashboardCache] Cache expired for: ${key}`);
      this.responseCache.delete(key);
      return null;
    }

    console.log(`[DashboardCache] Cache hit for: ${key}`);
    return cached.content;
  }

  setCachedResponse(tileId: string, tab: string, sector: string, content: string): void {
    const key = this.generateCacheKey(tileId, tab, sector);
    this.responseCache.set(key, {
      content,
      timestamp: Date.now()
    });
    console.log(`[DashboardCache] Response cached for: ${key}`);
  }

  // Chat History Cache Methods
  getChatHistory(tileId: string, tab: string, sector: string): ChatMessage[] {
    const key = this.generateCacheKey(tileId, tab, sector);
    const cached = this.chatCache.get(key);

    if (!cached) {
      console.log(`[DashboardCache] No chat history for: ${key}`);
      return [];
    }

    console.log(`[DashboardCache] Chat history loaded for: ${key}, ${cached.messages.length} messages`);
    return cached.messages;
  }

  setChatHistory(tileId: string, tab: string, sector: string, messages: ChatMessage[]): void {
    const key = this.generateCacheKey(tileId, tab, sector);
    this.chatCache.set(key, {
      messages,
      timestamp: Date.now()
    });
    console.log(`[DashboardCache] Chat history saved for: ${key}, ${messages.length} messages`);
  }

  // Clear methods
  clearResponseCache(tileId: string, tab: string, sector: string): void {
    const key = this.generateCacheKey(tileId, tab, sector);
    this.responseCache.delete(key);
    console.log(`[DashboardCache] Response cache cleared for: ${key}`);
  }

  clearChatHistory(tileId: string, tab: string, sector: string): void {
    const key = this.generateCacheKey(tileId, tab, sector);
    this.chatCache.delete(key);
    console.log(`[DashboardCache] Chat history cleared for: ${key}`);
  }

  clearAll(): void {
    this.responseCache.clear();
    this.chatCache.clear();
    console.log('[DashboardCache] All caches cleared');
  }
}

export const dashboardCache = DashboardCache.getInstance(); 