export enum NoteType {
  GENERAL = "general",
  SCENARIO = "scenario",
  DOCUMENT = "document"
}

export type Note = {
  uuid: string;
  user_uuid: string;
  sector_uuid?: string | null;
  title: string;
  content: string;
  note_type: NoteType;
  is_pinned: boolean;
  created_at: Date;
  updated_at: Date | null;
};

export type NoteCreate = {
  title: string;
  content: string;
  note_type: NoteType;
  is_pinned: boolean;
  sector_uuid?: string | null;
};

export type NoteUpdate = {
  title?: string;
  content?: string;
  note_type?: NoteType;
  is_pinned?: boolean;
  sector_uuid?: string | null;
};

export type NotesListResponse = {
  items: Note[];
  total: number;
};
