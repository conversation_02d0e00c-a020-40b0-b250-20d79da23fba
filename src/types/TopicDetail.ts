
export interface TopicDetailParams {
  tab: string;
  tileId: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: number;
}

export interface TopicSummary {
  content: string;
  isLoading: boolean;
  error: string | null;
}

export interface TopicChatState {
  messages: ChatMessage[];
  summary: TopicSummary;
}

export interface TopicDetailData {
  id: string;
  title: string;
  description?: string;
  relevance?: string;
  category?: string;
  type?: string;
  severity?: string;
  maturity?: string;
  impact?: string;
  [key: string]: any;
}
