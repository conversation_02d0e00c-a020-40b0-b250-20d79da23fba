
import { useState } from 'react';
import { useSearchSuggestions } from './useSearchSuggestions';
import { useSearchHistory } from './useSearchHistory';
import { useSearchResults } from './useSearchResults';
import { useSearchExecution } from './useSearchExecution';
import { SearchHistoryItem } from '@/components/QuerySearch/types';

export const useSearchQuery = () => {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Use the smaller, focused hooks
  const { 
    showSuggestions, 
    setShowSuggestions, 
    filteredSuggestions, 
    selectSuggestion 
  } = useSearchSuggestions(searchQuery);
  
  const {
    showHistory,
    setShowHistory,
    searchHistory,
    savedQueries,
    addToHistory,
    selectFromHistory: selectHistoryItem,
    toggleSaveQuery,
    isQuerySaved
  } = useSearchHistory();
  
  const {
    selectedCategory,
    setSelectedCategory,
    searchResults,
    hasSearched,
    setHasSearched,
    filterResults
  } = useSearchResults();
  
  const {
    isLoading,
    apiResponse,
    isJsonResponse,
    isGranular,
    setIsGranular,
    executeSearch
  } = useSearchExecution();

  // Handle search execution
  const handleSearch = async (selectedSector: string) => {
    const success = await executeSearch(searchQuery, selectedSector);
    
    if (success) {
      // Add to search history
      addToHistory(searchQuery);
      
      // Filter results based on search and category
      filterResults(searchQuery);
      
      setShowSuggestions(false);
    }
  };

  // Handle suggestion selection
  const selectFromSuggestion = (suggestion: string) => {
    setSearchQuery(suggestion);
    return selectSuggestion(suggestion);
  };

  // Handle history selection
  const selectFromHistory = (historyItem: SearchHistoryItem) => {
    setSearchQuery(selectHistoryItem(historyItem));
  };

  return {
    searchQuery,
    setSearchQuery,
    showSuggestions,
    setShowSuggestions,
    filteredSuggestions,
    selectedCategory,
    setSelectedCategory,
    searchResults,
    showHistory,
    setShowHistory,
    searchHistory,
    savedQueries,
    hasSearched,
    isLoading,
    apiResponse,
    isJsonResponse,
    isGranular,
    setIsGranular,
    handleSearch,
    selectSuggestion: selectFromSuggestion,
    selectFromHistory,
    toggleSaveQuery,
    isQuerySaved,
  };
};
