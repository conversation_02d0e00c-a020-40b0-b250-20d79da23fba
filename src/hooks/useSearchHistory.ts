
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { SearchHistoryItem, SavedQuery } from '@/components/QuerySearch/types';

export const useSearchHistory = () => {
  const { toast } = useToast();
  const [showHistory, setShowHistory] = useState(false);
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [savedQueries, setSavedQueries] = useState<SavedQuery[]>([]);

  const addToHistory = (query: string) => {
    const newHistoryItem: SearchHistoryItem = {
      query,
      timestamp: Date.now()
    };
    
    setSearchHistory(prev => [newHistoryItem, ...prev.slice(0, 9)]);
  };

  const selectFromHistory = (historyItem: SearchHistoryItem) => {
    setShowHistory(false);
    return historyItem.query;
  };

  const toggleSaveQuery = (query: string) => {
    const existing = savedQueries.find(item => item.query === query);
    
    if (existing) {
      setSavedQueries(savedQueries.filter(item => item.query !== query));
      toast({
        title: "Query Removed",
        description: "Query has been removed from saved list",
      });
    } else {
      const newSaved: SavedQuery = {
        id: Date.now(),
        query: query
      };
      setSavedQueries([...savedQueries, newSaved]);
      toast({
        title: "Query Saved",
        description: "Query has been added to your saved list",
      });
    }
  };

  const isQuerySaved = (query: string) => {
    return savedQueries.some(item => item.query === query);
  };

  return {
    showHistory,
    setShowHistory,
    searchHistory,
    savedQueries,
    addToHistory,
    selectFromHistory,
    toggleSaveQuery,
    isQuerySaved
  };
};
