import { useState, useEffect } from 'react';
import { useToastContext } from '@/contexts/ToastContext';
import {
  fetchTechnologies,
  fetchMaterials,
  fetchEnergies,
  fetchEmissions,
  createTechnology,
  associateTechnologyWithActivity
} from '@/services/connectionFormApi';
import {
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from '@/components/ConnectionForm/types';

// Hook for managing technologies (activity-specific)
export const useTechnologies = (activityUuid?: string) => {
  const [technologies, setTechnologies] = useState<TechnologyResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToastContext();

  const loadTechnologies = async () => {
    console.log('useTechnologies: loadTechnologies called with activityUuid:', activityUuid);

    if (!activityUuid) {
      console.log('useTechnologies: No activityUuid provided, setting empty technologies array');
      setTechnologies([]);
      return;
    }

    console.log('useTechnologies: Loading technologies for activity:', activityUuid);
    setIsLoading(true);
    setError(null);

    try {
      const data = await fetchTechnologies(activityUuid, { toast });
      console.log('useTechnologies: Successfully loaded technologies:', data);
      setTechnologies(data);
    } catch (err: any) {
      console.error('useTechnologies: Error loading technologies:', err);
      setError(err.message || 'Failed to load technologies');
    } finally {
      setIsLoading(false);
    }
  };

  const addTechnology = async (name: string, description?: string) => {
    if (!activityUuid) {
      toast({
        title: "Error",
        description: "No activity selected",
        variant: "destructive"
      });
      return null;
    }

    try {
      // Create the technology
      const newTechnology = await createTechnology({ name, description }, { toast });
      if (!newTechnology) return null;

      // Associate it with the activity
      const associated = await associateTechnologyWithActivity(activityUuid, newTechnology.uuid, { toast });
      if (!associated) return null;

      // Reload technologies to get the updated list
      await loadTechnologies();
      return newTechnology;
    } catch (err: any) {
      console.error('Error adding technology:', err);
      toast({
        title: "Error Adding Technology",
        description: err.message || "Failed to add technology",
        variant: "destructive"
      });
      return null;
    }
  };

  useEffect(() => {
    loadTechnologies();
  }, [activityUuid]);

  return {
    technologies,
    isLoading,
    error,
    addTechnology,
    reload: loadTechnologies
  };
};

// Hook for managing materials (sector-specific)
export const useMaterials = (sectorUuid?: string) => {
  const [materials, setMaterials] = useState<MaterialResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToastContext();

  const loadMaterials = async () => {
    if (!sectorUuid) {
      setMaterials([]);
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchMaterials(sectorUuid, { toast });
      setMaterials(data);
    } catch (err: any) {
      console.error('Error loading materials:', err);
      setError(err.message || 'Failed to load materials');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadMaterials();
  }, [sectorUuid]);

  return {
    materials,
    isLoading,
    error,
    reload: loadMaterials
  };
};

// Hook for managing energies (flat structure)
export const useEnergies = () => {
  const [energies, setEnergies] = useState<EnergyResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToastContext();

  const loadEnergies = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchEnergies({ toast });
      setEnergies(data);
    } catch (err: any) {
      console.error('Error loading energies:', err);
      setError(err.message || 'Failed to load energies');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadEnergies();
  }, []);

  return {
    energies,
    isLoading,
    error,
    reload: loadEnergies
  };
};

// Hook for managing emissions (flat structure)
export const useEmissions = () => {
  const [emissions, setEmissions] = useState<EmissionResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToastContext();

  const loadEmissions = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchEmissions({ toast });
      setEmissions(data);
    } catch (err: any) {
      console.error('Error loading emissions:', err);
      setError(err.message || 'Failed to load emissions');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadEmissions();
  }, []);

  return {
    emissions,
    isLoading,
    error,
    reload: loadEmissions
  };
};

// Combined hook for all connection form data
export const useConnectionFormData = (activityUuid?: string, sectorUuid?: string) => {
  const technologiesHook = useTechnologies(activityUuid);
  const materialsHook = useMaterials(sectorUuid);
  const energiesHook = useEnergies();
  const emissionsHook = useEmissions();

  const isLoading = technologiesHook.isLoading || materialsHook.isLoading || 
                   energiesHook.isLoading || emissionsHook.isLoading;

  const hasError = technologiesHook.error || materialsHook.error || 
                   energiesHook.error || emissionsHook.error;

  return {
    technologies: technologiesHook,
    materials: materialsHook,
    energies: energiesHook,
    emissions: emissionsHook,
    isLoading,
    hasError
  };
};
