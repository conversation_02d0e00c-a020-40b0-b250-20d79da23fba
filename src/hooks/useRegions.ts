import { fetchRegions } from '@/api/commonApi';
import { useState, useEffect, useCallback } from 'react';

export interface Region {
  uuid: string;
  code: string;
  name: string;
  created_at: string;
  updated_at: string;
}

// Module-level cache
let cachedRegions: Region[] | null = null;

export function useRegions(fetchOnMount = false) {
  const [regions, setRegions] = useState<Region[]>(cachedRegions || []);
  const [loadingRegions, setLoadingRegions] = useState(false);
  const [regionsError, setRegionsError] = useState<string | null>(null);

  const fetchRegionsNow = useCallback(async () => {
    if (cachedRegions && cachedRegions.length > 0) {
      setRegions(cachedRegions);
      return;
    }
    setLoadingRegions(true);
    setRegionsError(null);
    try {
      const data = await fetchRegions()
      cachedRegions = data;
      setRegions(data);
    } catch (err: any) {
      setRegionsError(err.message || 'Failed to fetch regions');
      setRegions([]);
    } finally {
      setLoadingRegions(false);
    }
  }, []);

  useEffect(() => {
    if (fetchOnMount && (!cachedRegions || cachedRegions.length === 0)) {
      fetchRegionsNow();
    }
  }, [fetchOnMount, fetchRegionsNow]);

  return { regions, loadingRegions, regionsError, fetchRegionsNow };
} 