
import { useState, useEffect } from 'react';
import { suggestionData } from '@/components/QuerySearch/data';

export const useSearchSuggestions = (searchQuery: string) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);

  // Filter suggestions based on search query
  useEffect(() => {
    if (searchQuery.length > 1) {
      const filtered = suggestionData.filter(item => 
        item.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredSuggestions(filtered);
      setShowSuggestions(filtered.length > 0);
    } else {
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  // When a suggestion is selected, return the full suggestion text
  const selectSuggestion = (suggestion: string) => {
    setShowSuggestions(false);
    return suggestion; // Return the entire suggestion
  };

  return {
    showSuggestions,
    setShowSuggestions,
    filteredSuggestions,
    selectSuggestion
  };
};
