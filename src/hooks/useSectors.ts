import { useState, useEffect } from 'react';
import { useToast } from './use-toast';

export interface Sector {
  uuid: string;
  name: string;
  default_flow: DefaultFlowData | null;
}

export interface DefaultFlowData {
  nodes: Record<string, DefaultFlowNode>;
  metadata: {
    createdAt: string;
    updatedAt: string;
  };
}

export interface DefaultFlowNode {
  activity: string;
  position: {
    x: number;
    y: number;
  };
  technologies: DefaultFlowTechnology[];
}

export interface DefaultFlowTechnology {
  name: string;
  inputs: {
    energies: DefaultFlowEnergy[];
    emissions: DefaultFlowEmission[];
    materials: DefaultFlowMaterial[];
  };
  endYear: number;
  outputs: {
    energies: DefaultFlowEnergy[];
    materials: DefaultFlowMaterial[];
  };
  isCustom: boolean;
  financial: {
    capacity: number;
    capitalCost: number;
    capacityUnit: string;
    capitalCostUnit: string;
    operatingMaintenanceCost: number;
    operatingMaintenanceCostUnit: string;
  };
  startYear: number;
  byproducts: {
    energies: DefaultFlowEnergy[];
    materials: DefaultFlowMaterial[];
  };
}

export interface DefaultFlowMaterial {
  id: string;
  cost?: number;
  unit: string;
  material: string;
  quantity: number;
  specificMaterialCost?: number;
  sourceNodeId?: string;
  sourceTechnology?: string;
  targetNodeId?: string;
  isFinalOutput?: boolean;
  targetTechnology?: string;
}

export interface DefaultFlowEnergy {
  id: string;
  cost?: number;
  unit: string;
  energy: string;
  quantity: number;
  specificEnergyCost?: number;
  targetNodeId?: string | null;
  isFinalOutput?: boolean;
  targetTechnology?: string | null;
}

export interface DefaultFlowEmission {
  id: string;
  cost?: number;
  unit: string;
  emission: string;
  emissionFactor: number;
}

export const useSectors = () => {
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSectors = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const accessToken = localStorage.getItem('accessToken');
        
        if (!accessToken) {
          setError('No access token found. Please log in again.');
          setIsLoading(false);
          return;
        }
        
        const response = await fetch('https://api.recre8.earth/sectors', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch sectors');
        }
        
        const data = await response.json();
        setSectors(data);
      } catch (err: unknown) {
        console.error('Error fetching sectors:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sectors';
        setError(errorMessage);
        toast({
          title: 'Error fetching sectors',
          description: errorMessage || 'Please try again later',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSectors();
  }, [toast]);
  
  return { sectors, isLoading, error };
};
