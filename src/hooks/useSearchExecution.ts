
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { callSearchApi } from '@/utils/searchApi';

export const useSearchExecution = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [apiResponse, setApiResponse] = useState<string | null>(null);
  const [isJsonResponse, setIsJsonResponse] = useState(false);
  const [isGranular, setIsGranular] = useState(false);
  
  const executeSearch = async (searchQuery: string, selectedSector: string) => {
    if (!searchQuery.trim()) {
      toast({
        title: "Empty Search",
        description: "Please enter a search query",
        variant: "destructive",
      });
      return false;
    }

    setIsLoading(true);
    setApiResponse(null);
    
    // Ensure we're sending the full query text and correct search_type based on isGranular
    const requestPayload = {
      query: searchQuery.trim(),
      search_type: isGranular ? "local" : "global" as "local" | "global",
      technology_sector: selectedSector, // Use the sector as received from the API
      question_type: "Default"
    };
    
    console.log("Executing search with payload:", requestPayload);
    
    const toastUtils = { toast, dismiss: () => {}, toasts: [] };
    const data = await callSearchApi(requestPayload, toastUtils);
    
    if (data) {
      // Extract and process the response
      const responseContent = data.response;
      
      // Check if response is JSON
      let isJson = false;
      try {
        if (typeof responseContent === 'string') {
          JSON.parse(responseContent);
          isJson = true;
        } else {
          isJson = typeof responseContent === 'object';
        }
      } catch (e) {
        isJson = false;
      }
      
      setIsJsonResponse(isJson);
      
      if (isJson && typeof responseContent === 'object') {
        setApiResponse(JSON.stringify(responseContent, null, 2));
      } else {
        setApiResponse(responseContent as string);
      }
      
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  return {
    isLoading,
    apiResponse,
    isJsonResponse,
    isGranular,
    setIsGranular,
    executeSearch
  };
};
