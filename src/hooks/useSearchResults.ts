
import { useState } from 'react';
import { resultsData } from '@/components/QuerySearch/data';

export const useSearchResults = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchResults, setSearchResults] = useState(resultsData);
  const [hasSearched, setHasSearched] = useState(false);
  
  const filterResults = (searchQuery: string, category: string = selectedCategory) => {
    let results = [...resultsData];
    
    if (category !== 'All') {
      results = results.filter(item => item.category === category);
    }
    
    results = results.filter(item => 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.summary.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setSearchResults(results);
    setHasSearched(true);
    
    return results;
  };

  return {
    selectedCategory,
    setSelectedCategory,
    searchResults,
    hasSearched,
    setHasSearched,
    filterResults
  };
};
